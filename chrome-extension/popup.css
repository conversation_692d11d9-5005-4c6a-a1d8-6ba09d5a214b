/* Google Trends Data Downloader - Popup Styles */

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 14px;
  color: #333;
  background-color: #f5f5f5;
  width: 500px; /* Increased width for more horizontal space */
  height: 550px; /* Increased height to reduce scrolling */
  overflow-y: auto;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

header {
  background-color: #4285f4;
  color: white;
  padding: 15px;
  text-align: center;
}

h1 {
  font-size: 18px;
  font-weight: 500;
}

h2 {
  font-size: 16px;
  margin-bottom: 10px;
  color: #4285f4;
}

.content {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
}

#status-message {
  margin-top: 15px;
  padding: 10px 12px;
  border-radius: 4px;
  background-color: #f8f9fa;
  border-left: 4px solid #4285f4;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

#status-message.error {
  background-color: #fef7f6;
  color: #d93025;
  border-left-color: #d93025;
}

#status-message.success {
  background-color: #f3faf5;
  color: #0f9d58;
  border-left-color: #0f9d58;
}

#status-message.info {
  background-color: #e8f0fe;
  color: #1a73e8;
  border-left-color: #1a73e8;
}

a {
  color: #4285f4;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

footer {
  padding: 20px;
  display: flex;
  justify-content: center;
  background-color: white;
  border-top: 1px solid #eee;
}

footer button {
  width: 80%;
  padding: 12px 20px;
  font-size: 16px;
  min-height: 50px;
  background-color: #0F9D58; /* Google green */
}

footer button:hover {
  background-color: #0b8043; /* Darker green */
}

button {
  background-color: #4285f4;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 18px;
  cursor: pointer;
  font-size: 15px;
  font-weight: 500;
  transition: background-color 0.2s;
  min-height: 40px; /* Ensure buttons have a good height */
}

button:hover {
  background-color: #3367d6;
}

button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

/* Batch Download Styles */
#batch-download-container {
  background-color: white;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.batch-controls {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.url-input-row {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: nowrap; /* Prevent wrapping */
}

#url-input {
  flex: 1;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 14px;
  height: 20px; /* Explicit height for better visibility */
}

.icon-button {
  width: 30px;
  height: 30px;
  min-width: 30px; /* Prevent stretching */
  min-height: 30px; /* Prevent stretching */
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #4285f4;
  color: white;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  border: none;
  padding: 0;
  line-height: 1;
  flex-shrink: 0; /* Prevent shrinking */
  aspect-ratio: 1 / 1; /* Maintain perfect circle */
}

.icon-button:hover {
  background-color: #3367d6;
}

.icon-button.remove {
  background-color: #ea4335;
  font-size: 16px;
}

.icon-button.remove:hover {
  background-color: #d93025;
}

#url-list-container {
  margin-top: 5px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  max-height: 250px; /* Increased height for the URL list */
  overflow-y: auto;
}

#url-list-container h3 {
  font-size: 14px;
  margin: 0;
  padding: 8px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
}

.url-list {
  padding: 0;
}

.url-item {
  display: flex;
  padding: 10px;
  border-bottom: 1px solid #eee;
  align-items: center;
  gap: 10px;
  flex-wrap: nowrap; /* Prevent wrapping */
}

.url-item:last-child {
  border-bottom: none;
}

.url-text {
  flex: 1;
  font-size: 14px;
  word-break: break-word;
  line-height: 1.4;
  padding: 4px 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
  cursor: pointer; /* Show pointer cursor to indicate clickable */
  font-weight: 500;
  color: #1a73e8;
  transition: background-color 0.2s, color 0.2s;
  position: relative;
}

.url-text:hover {
  background-color: #e8f0fe;
  color: #0d47a1;
}

.url-text:active {
  background-color: #d2e3fc;
}

/* Add a subtle indicator that it's clickable */
.url-text::after {
  content: '↗';
  position: absolute;
  right: 8px;
  top: 4px;
  font-size: 12px;
  opacity: 0.7;
}

.empty-list-message {
  padding: 12px 8px;
  color: #666;
  font-style: italic;
  text-align: center;
  font-size: 12px;
}

.url-lists {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

#saved-url-lists {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
}

.batch-buttons {
  display: flex;
  gap: 10px;
  margin-top: 5px;
}

.batch-download-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 15px;
}

.batch-download-buttons button {
  padding: 12px 20px;
  font-size: 16px;
  min-height: 45px;
}

#batch-progress {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.hidden {
  display: none;
}

.progress-container {
  margin: 10px 0;
}

.progress-bar {
  width: 100%;
  height: 10px;
  background-color: #f1f1f1;
  border-radius: 5px;
  overflow: hidden;
  margin-bottom: 5px;
}

#progress-fill {
  height: 100%;
  background-color: #4285f4;
  width: 0%;
  transition: width 0.3s;
}

#progress-text {
  font-size: 12px;
  color: #666;
  text-align: center;
}

#current-url-container {
  margin: 15px 0;
  font-size: 13px;
}

#current-url {
  word-break: break-word;
  background-color: #f8f9fa;
  padding: 8px 10px;
  border-radius: 4px;
  margin-top: 5px;
  max-height: 60px;
  overflow-y: auto;
  font-weight: 500;
  color: #1a73e8;
  cursor: pointer; /* Show pointer cursor to indicate clickable */
  transition: background-color 0.2s, color 0.2s;
  position: relative;
}

#current-url:hover {
  background-color: #e8f0fe;
  color: #0d47a1;
}

#current-url:active {
  background-color: #d2e3fc;
}

/* Add a subtle indicator that it's clickable */
#current-url::after {
  content: '↗';
  position: absolute;
  right: 8px;
  top: 8px;
  font-size: 12px;
  opacity: 0.7;
}

#cancel-batch-btn {
  background-color: #ea4335;
  width: 100%;
  margin-top: 10px;
}

#cancel-batch-btn:hover {
  background-color: #d93025;
}
