/**
 * Test script to check if the Chrome extension is properly loading the node-integration.js module
 * 
 * This script will:
 * 1. Check if the background.js file is properly importing node-integration.js
 * 2. Check if the WebSocket connection is being established
 * 3. Check if the extension can send and receive messages from the Node.js app
 */

// Import the node-integration.js module
import './node-integration.js';

// Test if the module is loaded correctly
console.log('Testing node-integration.js module...');
console.log('window.nodeIntegration:', window.nodeIntegration);

// Test if the WebSocket connection can be established
if (window.nodeIntegration) {
  console.log('Initializing node integration...');
  window.nodeIntegration.init();
  
  // Wait for 3 seconds to allow the WebSocket connection to be established
  setTimeout(() => {
    console.log('Checking WebSocket connection...');
    
    // Send a test message to the Node.js app
    window.nodeIntegration.sendWebSocketMessage({
      type: 'test',
      message: 'Test message from extension'
    });
    
    console.log('Test message sent to Node.js app');
  }, 3000);
} else {
  console.error('window.nodeIntegration is not defined');
}
