/**
 * Chrome Extension Update Instructions
 * 
 * To implement the close_tabs functionality in the Chrome extension, follow these steps:
 * 
 * 1. Add the close_tabs handler to node-integration-worker.js:
 *    - Find the handleWebSocketMessage function
 *    - Add a new case for 'close_tabs' in the switch statement
 * 
 * Here's the code to add:
 * 
 * ```javascript
 * case 'close_tabs':
 *   handleCloseTabs();
 *   break;
 * ```
 * 
 * 2. Add the handleCloseTabs function to node-integration-worker.js:
 * 
 * ```javascript
 * /**
 *  * Handle close tabs request from Node.js
 *  */
 * function handleCloseTabs() {
 *   console.log('Received close tabs request');
 *   
 *   // Get all tabs
 *   chrome.tabs.query({}, (tabs) => {
 *     // Find Google Trends tabs
 *     const trendsTabs = tabs.filter(tab => 
 *       tab.url && tab.url.includes('trends.google.com')
 *     );
 *     
 *     console.log(`Found ${trendsTabs.length} Google Trends tabs to close`);
 *     
 *     // Close each tab
 *     trendsTabs.forEach(tab => {
 *       chrome.tabs.remove(tab.id, () => {
 *         if (chrome.runtime.lastError) {
 *           console.error(`Error closing tab ${tab.id}:`, chrome.runtime.lastError);
 *         } else {
 *           console.log(`Closed tab ${tab.id}`);
 *         }
 *       });
 *     });
 *     
 *     // Send confirmation back to server
 *     sendWebSocketMessage({
 *       type: 'tabs_closed',
 *       count: trendsTabs.length,
 *       timestamp: Date.now()
 *     });
 *   });
 * }
 * ```
 * 
 * 3. Add the same handler to node-integration.js if it's being used:
 * 
 * ```javascript
 * case 'close_tabs':
 *   handleCloseTabs();
 *   break;
 * ```
 * 
 * 4. Add the handleCloseTabs function to node-integration.js:
 * 
 * ```javascript
 * /**
 *  * Handle close tabs request from Node.js
 *  */
 * function handleCloseTabs() {
 *   console.log('Received close tabs request');
 *   
 *   // Forward the request to the background script
 *   chrome.runtime.sendMessage({
 *     action: 'closeTabs'
 *   });
 *   
 *   // Send confirmation back to server
 *   sendWebSocketMessage({
 *     type: 'tabs_closed',
 *     timestamp: Date.now()
 *   });
 * }
 * ```
 * 
 * 5. Add a handler for the 'closeTabs' action in background.js:
 * 
 * ```javascript
 * // Handle close tabs request
 * if (message.action === 'closeTabs') {
 *   console.log('Received close tabs request from Node.js');
 *   
 *   // Get all tabs
 *   chrome.tabs.query({}, (tabs) => {
 *     // Find Google Trends tabs
 *     const trendsTabs = tabs.filter(tab => 
 *       tab.url && tab.url.includes('trends.google.com')
 *     );
 *     
 *     console.log(`Found ${trendsTabs.length} Google Trends tabs to close`);
 *     
 *     // Close each tab
 *     trendsTabs.forEach(tab => {
 *       chrome.tabs.remove(tab.id, () => {
 *         if (chrome.runtime.lastError) {
 *           console.error(`Error closing tab ${tab.id}:`, chrome.runtime.lastError);
 *         } else {
 *           console.log(`Closed tab ${tab.id}`);
 *         }
 *       });
 *     });
 *   });
 *   
 *   return true;
 * }
 * ```
 * 
 * These changes will allow the Node.js server to send a request to close all Google Trends tabs,
 * which will help ensure that each URL is processed one at a time in the batch download process.
 */
