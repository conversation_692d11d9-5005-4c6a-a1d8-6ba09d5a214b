<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Google Trends Data Downloader - Background Page</title>
  <script src="node-integration.js"></script>
  <script src="background.js"></script>
  <script>
    // Keep the background page alive
    function keepAlive() {
      // Send a heartbeat to the Node.js server every 25 seconds
      setInterval(() => {
        if (window.nodeIntegration && typeof window.nodeIntegration.sendWebSocketMessage === 'function') {
          console.log('Sending heartbeat to keep connection alive');
          window.nodeIntegration.sendWebSocketMessage({
            type: 'heartbeat',
            timestamp: Date.now()
          });
        }
      }, 25000);
    }

    // Initialize the keep-alive mechanism when the page loads
    window.addEventListener('load', keepAlive);
  </script>
</head>
<body>
  <!-- This page is invisible to the user but keeps the WebSocket connection alive -->
  <div id="status">Background page active</div>
</body>
</html>
