/**
 * Node.js Integration Module for Service Worker
 * Handles communication between the Chrome extension and a Node.js server
 */

import * as wsStabilizer from './websocket-stabilizer.js';

// Configuration
const NODE_SERVER_URL = 'http://localhost:3000'; // Default Node.js server URL
const WS_SERVER_URL = 'ws://localhost:3000';     // WebSocket server URL (same as HTTP port)

// Extension ID for identification
let extensionId = '';

// Track if we're initialized
let isInitialized = false;

/**
 * Initialize the Node.js integration
 */
export function initNodeIntegration() {
  if (isInitialized) {
    console.log('Node.js integration already initialized');

    // Check connection status and reconnect if needed
    const status = wsStabilizer.getConnectionStatus();
    console.log('Current WebSocket connection status:', status);

    if (!status.isConnected) {
      console.log('Connection is not active, attempting to reconnect');
      wsStabilizer.forceReconnect();
    }

    return;
  }

  console.log('Initializing Node.js integration (Service Worker)');

  // Get the extension ID
  extensionId = chrome.runtime.id;
  console.log('Extension ID:', extensionId);

  // Initialize the WebSocket stabilizer
  wsStabilizer.initWebSocketStabilizer();

  // Register event handlers
  wsStabilizer.on('open', () => {
    console.log('WebSocket connection opened, sending hello message');

    // Send a hello message when connected
    sendWebSocketMessage({
      type: 'hello',
      message: 'Chrome extension connected',
      extensionId: extensionId,
      version: '1.0',
      capabilities: {
        downloadRelatedQueries: true,
        batchDownload: true
      },
      timestamp: Date.now()
    });
  });

  wsStabilizer.on('close', (event) => {
    console.log(`WebSocket connection closed with code ${event.code}, reason: ${event.reason || 'No reason provided'}`);
  });

  wsStabilizer.on('error', (event) => {
    console.error('WebSocket error:', event);
  });

  wsStabilizer.on('message', handleWebSocketMessage);

  // Mark as initialized
  isInitialized = true;

  // Set up a periodic connection check
  setInterval(() => {
    const status = wsStabilizer.getConnectionStatus();
    console.log('Periodic connection status check:', status);

    if (!status.isConnected && !status.isConnecting) {
      console.log('Connection is down during periodic check, attempting to reconnect');
      wsStabilizer.forceReconnect();
    }
  }, 60000); // Check every minute
}

/**
 * Handle WebSocket messages
 */
function handleWebSocketMessage(data) {
  console.log('Message from server:', data);

  // Handle different message types
  switch (data.type) {
    case 'download_request':
      handleDownloadRequest(data);
      break;
    case 'ping':
      // Respond with pong to keep connection alive
      sendWebSocketMessage({
        type: 'pong',
        extensionId: extensionId,
        timestamp: Date.now()
      });
      break;
    case 'welcome':
      console.log('Received welcome message from server:', data.message);
      break;
    case 'check_status':
      // Respond with current status
      sendWebSocketMessage({
        type: 'status_response',
        extensionId: extensionId,
        status: 'active',
        timestamp: Date.now()
      });
      break;
    case 'close_tabs':
      console.log('Received close tabs request from server');
      // Forward the request to the background script
      chrome.runtime.sendMessage({
        action: 'closeTabs'
      }, response => {
        console.log('Received response from background script for close tabs request:', response);

        // Send acknowledgment back to the Node.js server
        sendWebSocketMessage({
          type: 'tabs_closed',
          message: 'Tabs closing request received',
          timestamp: Date.now()
        });
      });
      break;
    case 'heartbeat_response':
      console.log('Received heartbeat response from server');
      break;
    case 'force_reconnect':
      console.log('Received force reconnect request from server');
      // Send acknowledgment before reconnecting
      sendWebSocketMessage({
        type: 'reconnect_acknowledged',
        extensionId: extensionId,
        message: 'Reconnection request received, reconnecting now',
        timestamp: Date.now()
      });

      // Log the current connection status
      const status = wsStabilizer.getConnectionStatus();
      console.log('Connection status before forced reconnect:', status);

      // Force reconnection with a slight delay to allow the acknowledgment to be sent
      setTimeout(() => {
        console.log('Executing forced reconnection');
        wsStabilizer.forceReconnect();
      }, 500);
      break;
    default:
      console.log('Unknown message type:', data.type);
  }
}

/**
 * Handle download request from Node.js
 */
function handleDownloadRequest(message) {
  try {
    const { url, options, requestId } = message;

    if (!url) {
      sendWebSocketMessage({
        type: 'error',
        error: 'No URL provided',
        requestId: requestId
      });
      return;
    }

    console.log('Received download request for URL:', url);
    console.log('Request ID:', requestId);
    console.log('Options:', options);

    // Ensure onlyRelatedQueries is a proper boolean
    const relatedQueriesFlag = options?.onlyRelatedQueries === true;
    console.log('onlyRelatedQueries value (converted to boolean):', relatedQueriesFlag);

    // Acknowledge receipt of the request
    sendWebSocketMessage({
      type: 'download_request_received',
      requestId,
      url,
      timestamp: Date.now()
    });

    // Use a different approach to communicate with the background script
    try {
      // First, register a listener for download completion or failure
    const downloadListener = function(msg) {
      if (msg.action === 'downloadComplete' && msg.requestId === requestId) {
        console.log('Download complete message received:', msg);

        // Forward the completion message to the Node.js server
        sendWebSocketMessage({
          type: 'download_complete',
          requestId,
          fileName: msg.fileName || 'unknown',
          stats: msg.stats || {},
          timestamp: Date.now()
        });

        // Wait a moment to ensure the download has been processed
        setTimeout(() => {
          // Close tabs after download completion
          closeTabsAfterDownload(msg.tabId, requestId);
        }, 2000); // Wait 2 seconds before closing tabs

        // Remove this listener
        chrome.runtime.onMessage.removeListener(downloadListener);
        return true;
      } else if (msg.action === 'downloadFailed' && msg.requestId === requestId) {
        console.log('Download failed message received:', msg);

        // Forward the failure message to the Node.js server
        sendWebSocketMessage({
          type: 'download_failed',
          requestId,
          error: msg.error || 'Unknown error',
          timestamp: Date.now()
        });

        // Remove this listener
        chrome.runtime.onMessage.removeListener(downloadListener);
        return true;
      }
      return false;
    };

    // Register the listener
    chrome.runtime.onMessage.addListener(downloadListener);

    // Set a timeout to clean up the listener if no response is received
    setTimeout(() => {
      chrome.runtime.onMessage.removeListener(downloadListener);

      // Before sending a timeout message, check if any downloads have completed
      chrome.downloads.search({}, (downloads) => {
        // Look for recent Google Trends CSV downloads
        const recentTrendsDownloads = downloads.filter(download =>
          download.filename.includes('relatedQueries') &&
          download.filename.endsWith('.csv') &&
          download.state === 'complete' &&
          download.endTime &&
          (new Date().getTime() - new Date(download.endTime).getTime() < 120000) // Within the last 2 minutes
        );

        if (recentTrendsDownloads.length > 0) {
          console.log('Found recent Google Trends downloads:', recentTrendsDownloads);

          // Send a success message instead of timeout
          sendWebSocketMessage({
            type: 'download_complete',
            requestId,
            fileName: recentTrendsDownloads[0].filename,
            timestamp: Date.now()
          });

          // Close tabs after download completion
          closeTabsAfterDownload(null, requestId);
        } else {
          // Send a timeout message if we haven't received a response and no downloads found
          sendWebSocketMessage({
            type: 'download_failed',
            requestId,
            error: 'Timeout waiting for download response from background script',
            timestamp: Date.now()
          });
        }
      });
    }, 90000); // 1.5 minute timeout (increased from 1 minute)

    // Use a more reliable method to communicate with the background script
    // First, try to wake up the service worker by sending a ping
    console.log('Attempting to wake up the service worker...');

    // Method 1: Try to use chrome.runtime.getBackgroundPage (works in Manifest V2)
    try {
      if (typeof chrome.runtime.getBackgroundPage === 'function') {
        chrome.runtime.getBackgroundPage(function(backgroundPage) {
          if (backgroundPage && backgroundPage.startBatchDownload) {
            console.log('Got background page, calling startBatchDownload directly');
            console.log('onlyRelatedQueries value:', options?.onlyRelatedQueries);

            // Ensure onlyRelatedQueries is a proper boolean
            const relatedQueriesFlag = options?.onlyRelatedQueries === true;
            console.log('Converted onlyRelatedQueries to boolean:', relatedQueriesFlag);

            backgroundPage.startBatchDownload(
              [url],
              relatedQueriesFlag,
              { isNodeRequest: true, requestId }
            );

            // Send a processing message to the server
            sendWebSocketMessage({
              type: 'download_request_processing',
              requestId,
              success: true,
              message: 'Download request sent directly to background page',
              timestamp: Date.now()
            });
            return;
          } else {
            console.log('Background page found but startBatchDownload not available');
          }
        });
      }
    } catch (error) {
      console.log('getBackgroundPage not available or failed:', error);
    }

    // Method 2: Try to use chrome.tabs.create to open a new tab that will trigger the download
    try {
      console.log('Trying to create a new tab to trigger the download');
      chrome.tabs.create({
        url: url,
        active: true
      }, function(tab) {
        if (chrome.runtime.lastError) {
          console.error('Error creating tab:', chrome.runtime.lastError);
          return;
        }

        console.log('Tab created, ID:', tab.id);

        // Store the tab ID in a variable accessible to the download completion handler
        const createdTabId = tab.id;

        // Wait for the tab to load and for our buttons to be injected
        setTimeout(() => {
          // Try to inject a content script that will trigger the download
          chrome.scripting.executeScript({
            target: { tabId: tab.id },
            function: (onlyRelatedQueries) => {
              console.log('Injected script running in tab, onlyRelatedQueries =', onlyRelatedQueries);

              // Set onlyRelatedQueries to true explicitly in the window.state
              if (typeof window.state !== 'undefined') {
                window.state.onlyRelatedQueries = true;
                console.log('Set window.state.onlyRelatedQueries = true');
              }

              // Try to find and click the "Download related queries" button
              const downloadRelatedQueriesBtn = document.getElementById('gtrends-download-related-queries-btn');
              if (downloadRelatedQueriesBtn) {
                console.log('Found Download related queries button, clicking it');
                downloadRelatedQueriesBtn.click();
                return true;
              }

              // If the button is not found, try to find and click export buttons directly
              const exportButtons = document.querySelectorAll('button.export');
              if (exportButtons.length > 0) {
                console.log(`Found ${exportButtons.length} export buttons, clicking the first one`);
                exportButtons[0].click();

                // Wait a bit and try to click the CSV option
                setTimeout(() => {
                  const csvOptions = document.querySelectorAll('button.md-button[ng-click*="csv"], md-menu-item button[ng-click*="csv"]');
                  if (csvOptions.length > 0) {
                    console.log(`Found ${csvOptions.length} CSV options, clicking the first one`);
                    csvOptions[0].click();
                  }
                }, 1000);

                return true;
              }

              return false;
            },
            args: [options?.onlyRelatedQueries === true]
          }).then(results => {
            console.log('Script execution results:', results);

            // Send a processing message to the server
            sendWebSocketMessage({
              type: 'download_request_processing',
              requestId,
              success: true,
              message: 'Download triggered via tab injection',
              tabId: createdTabId, // Include the tab ID for later closing
              timestamp: Date.now()
            });
          }).catch(error => {
            console.error('Error executing script in tab:', error);

            // Notify the server about the error
            sendWebSocketMessage({
              type: 'download_request_error',
              requestId,
              error: `Error executing script in tab: ${error.message || 'Unknown error'}`,
              timestamp: Date.now()
            });
          });
        }, 8000); // Wait 8 seconds for the page to fully load and buttons to be injected
      });
    } catch (tabError) {
      console.error('Error creating tab:', tabError);

      // Method 3: Fall back to the original method as a last resort
      console.log('Falling back to chrome.runtime.sendMessage...');
      // Ensure onlyRelatedQueries is a proper boolean
      const relatedQueriesFlag = options?.onlyRelatedQueries === true;
      console.log('Sending message to background script with onlyRelatedQueries =', relatedQueriesFlag);

      chrome.runtime.sendMessage({
        action: 'nodeDownloadRequest',
        data: {
          url,
          options: {
            ...options,
            onlyRelatedQueries: relatedQueriesFlag
          },
          requestId
        }
      }, function(response) {
        if (chrome.runtime.lastError) {
          console.error('Error sending message to background script:', chrome.runtime.lastError);

          // Notify the server about the error
          sendWebSocketMessage({
            type: 'download_request_error',
            requestId,
            error: `Error sending message to background script: ${chrome.runtime.lastError.message || 'Unknown error'}`,
            timestamp: Date.now()
          });
          return;
        }

        console.log('Download request sent to background script, response:', response);

        // Send a processing message to the server
        sendWebSocketMessage({
          type: 'download_request_processing',
          requestId,
          success: true,
          message: 'Download request sent to background script',
          timestamp: Date.now()
        });
      });
    }

    console.log('Download request processing initiated');

    } catch (error) {
      console.error('Error sending message to background script:', error);

      // Notify the server about the error
      sendWebSocketMessage({
        type: 'download_request_error',
        requestId,
        error: `Error sending message to background script: ${error.message}`,
        timestamp: Date.now()
      });
    }
  } catch (outerError) {
    console.error('Error in handleDownloadRequest:', outerError);

    // Notify the server about the error
    if (message && message.requestId) {
      sendWebSocketMessage({
        type: 'download_request_error',
        requestId: message.requestId,
        error: `Error in handleDownloadRequest: ${outerError.message}`,
        timestamp: Date.now()
      });
    }
  }
}

/**
 * Close tabs after download completes
 */
function closeTabsAfterDownload(tabId, requestId) {
  console.log('Closing tabs after download completion');

  // First try to close the specific tab if provided
  if (tabId) {
    try {
      chrome.tabs.remove(tabId, () => {
        if (chrome.runtime.lastError) {
          console.error(`Error closing tab ${tabId}:`, chrome.runtime.lastError);
        } else {
          console.log(`Successfully closed tab ${tabId} after download completion`);

          // Send confirmation back to server for this specific tab
          sendWebSocketMessage({
            type: 'tabs_closed',
            count: 1,
            message: 'Tab closed after download completion',
            requestId: requestId,
            timestamp: Date.now()
          });
        }
      });
    } catch (error) {
      console.error(`Error closing tab ${tabId}:`, error);
    }
  }

  // Then close all Google Trends tabs
  chrome.tabs.query({url: "https://trends.google.com/*"}, (tabs) => {
    console.log(`Found ${tabs.length} Google Trends tabs to close after download completion`);

    if (tabs.length > 0) {
      let closedCount = 0;

      // Function to close tabs one by one to avoid race conditions
      function closeNextTab(index) {
        if (index >= tabs.length) {
          // All tabs closed, send confirmation
          sendWebSocketMessage({
            type: 'tabs_closed',
            count: closedCount,
            message: 'All Google Trends tabs closed after download completion',
            requestId: requestId,
            timestamp: Date.now()
          });
          return;
        }

        const tab = tabs[index];
        try {
          chrome.tabs.remove(tab.id, () => {
            if (chrome.runtime.lastError) {
              console.error(`Error closing Google Trends tab ${tab.id}:`, chrome.runtime.lastError);
            } else {
              console.log(`Closed Google Trends tab ${tab.id} after download completion`);
              closedCount++;
            }
            // Process next tab
            closeNextTab(index + 1);
          });
        } catch (error) {
          console.error(`Error closing Google Trends tab ${tab.id}:`, error);
          // Continue with next tab even if there was an error
          closeNextTab(index + 1);
        }
      }

      // Start closing tabs
      closeNextTab(0);
    } else {
      // No Google Trends tabs found, send confirmation
      sendWebSocketMessage({
        type: 'tabs_closed',
        count: 0,
        message: 'No Google Trends tabs found to close',
        requestId: requestId,
        timestamp: Date.now()
      });
    }
  });
}

/**
 * Send message through WebSocket
 */
export function sendWebSocketMessage(message) {
  console.log('Sending WebSocket message:', message);
  return wsStabilizer.sendMessage(message);
}

/**
 * Send download result to Node.js server
 */
export function sendDownloadResult(data) {
  const { fileData, fileName, requestId } = data;

  // Send notification through WebSocket
  sendWebSocketMessage({
    type: 'download_complete',
    fileName,
    requestId,
    extensionId: extensionId
  });

  // Send the actual data through HTTP POST
  fetch(`${NODE_SERVER_URL}/api/receive-data`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      fileName,
      fileData,
      requestId,
      extensionId: extensionId
    })
  })
  .then(response => response.json())
  .then(result => {
    console.log('Data sent to Node.js server:', result);
  })
  .catch(error => {
    console.error('Error sending data to Node.js server:', error);
  });
}

/**
 * Get the current connection status
 */
export function getConnectionStatus() {
  return wsStabilizer.getConnectionStatus();
}

/**
 * Force a reconnection to the WebSocket server
 */
export function reconnect() {
  return wsStabilizer.forceReconnect();
}
