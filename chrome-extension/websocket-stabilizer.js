/**
 * WebSocket Stabilizer Module
 *
 * This module helps stabilize WebSocket connections by implementing:
 * 1. Connection pooling
 * 2. Automatic reconnection with exponential backoff
 * 3. Message queuing for offline periods
 * 4. Heartbeat mechanism
 */

// Configuration
const WS_SERVER_URL = 'ws://localhost:3000';
const HEARTBEAT_INTERVAL = 20000; // 20 seconds (reduced from 30s)
const RECONNECT_INTERVAL = 2000; // 2 seconds (reduced from 3s)
const MAX_RECONNECT_ATTEMPTS = 20; // Increased from 15
const HEARTBEAT_TIMEOUT = 60000; // 60 seconds (3x heartbeat interval)
const CONNECTION_CHECK_INTERVAL = 10000; // 10 seconds

// State
let activeConnection = null;
let connectionAttempts = 0;
let heartbeatInterval = null;
let connectionCheckInterval = null;
let messageQueue = [];
let isConnecting = false;
let lastHeartbeatResponse = 0;

// Event callbacks
const eventCallbacks = {
  open: [],
  close: [],
  message: [],
  error: []
};

/**
 * Initialize the WebSocket stabilizer
 */
export function initWebSocketStabilizer() {
  console.log('Initializing WebSocket stabilizer');
  connect();
}

/**
 * Connect to the WebSocket server
 */
function connect() {
  // Check if we're already connecting or have an active connection
  if (isConnecting) {
    console.log('Already attempting to connect, skipping duplicate connection attempt');
    return;
  }

  if (activeConnection && activeConnection.readyState === WebSocket.OPEN) {
    console.log('Connection already open, skipping connection attempt');
    return;
  }

  if (activeConnection && activeConnection.readyState === WebSocket.CONNECTING) {
    console.log('Connection already in progress, skipping duplicate connection attempt');
    return;
  }

  isConnecting = true;
  console.log(`Attempting to connect to WebSocket server (attempt ${connectionAttempts + 1}/${MAX_RECONNECT_ATTEMPTS})`);
  console.log(`Server URL: ${WS_SERVER_URL}`);

  try {
    // Close existing connection if any
    if (activeConnection && (activeConnection.readyState === WebSocket.OPEN || activeConnection.readyState === WebSocket.CONNECTING)) {
      console.log(`Closing existing connection in state: ${activeConnection.readyState}`);
      try {
        activeConnection.close(1000, "Reconnecting");
      } catch (e) {
        console.error('Error closing existing connection:', e);
      }

      // Small delay to ensure the previous connection is properly closed
      setTimeout(() => {
        createNewConnection();
      }, 500);
    } else {
      createNewConnection();
    }
  } catch (error) {
    console.error('Error in connect function:', error);
    isConnecting = false;
    handleReconnect();
  }
}

/**
 * Create a new WebSocket connection
 */
function createNewConnection() {
  try {
    // Create new WebSocket connection
    console.log('Creating new WebSocket connection to', WS_SERVER_URL);
    activeConnection = new WebSocket(WS_SERVER_URL);

    // Set a timeout to detect connection failures faster
    const connectionTimeout = setTimeout(() => {
      if (activeConnection && activeConnection.readyState === WebSocket.CONNECTING) {
        console.log('WebSocket connection attempt timed out');
        try {
          activeConnection.close(1000, "Connection timeout");
        } catch (e) {
          console.error('Error closing timed out connection:', e);
        }
        handleReconnect();
      }
    }, 10000); // 10 second timeout for connection attempts

    // Connection opened
    activeConnection.addEventListener('open', (event) => {
      console.log('WebSocket connection established successfully');
      clearTimeout(connectionTimeout); // Clear the timeout
      isConnecting = false;
      connectionAttempts = 0;
      lastHeartbeatResponse = Date.now();

      // Log connection details
      console.log(`Connection details: readyState=${activeConnection.readyState}, url=${activeConnection.url}`);

      // Process any queued messages
      processMessageQueue();

      // Set up heartbeat
      setupHeartbeat();

      // Trigger open callbacks
      triggerCallbacks('open', event);
    });

    // Listen for messages
    activeConnection.addEventListener('message', (event) => {
      try {
        const data = JSON.parse(event.data);

        // Update heartbeat timestamp for any message received
        lastHeartbeatResponse = Date.now();

        // Handle ping messages
        if (data.type === 'ping') {
          console.log('Received ping from server, sending pong');
          sendMessage({
            type: 'pong',
            timestamp: Date.now()
          });
        }

        // Handle heartbeat response messages
        if (data.type === 'heartbeat_response') {
          console.log('Received heartbeat response from server');
          // Update the heartbeat timestamp explicitly
          lastHeartbeatResponse = Date.now();
        }

        // Trigger message callbacks
        triggerCallbacks('message', data);
      } catch (error) {
        console.error('Error parsing message:', error);
      }
    });

    // Connection closed
    activeConnection.addEventListener('close', (event) => {
      console.log('WebSocket connection closed');
      console.log('Close event code:', event.code);
      console.log('Close event reason:', event.reason);

      isConnecting = false;
      clearInterval(heartbeatInterval);

      // Trigger close callbacks
      triggerCallbacks('close', event);

      // Attempt to reconnect
      handleReconnect();
    });

    // Connection error
    activeConnection.addEventListener('error', (event) => {
      console.error('WebSocket error:', event);
      isConnecting = false;

      // Trigger error callbacks
      triggerCallbacks('error', event);
    });
  } catch (error) {
    console.error('Error creating WebSocket connection:', error);
    isConnecting = false;
    handleReconnect();
  }
}

/**
 * Handle reconnection with exponential backoff
 */
function handleReconnect() {
  // Make sure we're not already connecting
  if (isConnecting) {
    console.log('Already attempting to connect, skipping reconnection');
    return;
  }

  if (connectionAttempts < MAX_RECONNECT_ATTEMPTS) {
    connectionAttempts++;

    // Calculate delay with a bit of randomness to prevent synchronized reconnection attempts
    const baseDelay = RECONNECT_INTERVAL * Math.pow(1.5, connectionAttempts - 1);
    const jitter = Math.random() * 1000; // Add up to 1 second of random jitter
    const delay = baseDelay + jitter;

    console.log(`Reconnecting in ${Math.round(delay)}ms (attempt ${connectionAttempts}/${MAX_RECONNECT_ATTEMPTS})`);

    // Set a timeout to attempt reconnection
    setTimeout(() => {
      // Double-check we're not already connecting before proceeding
      if (!isConnecting) {
        connect();
      } else {
        console.log('Connection already in progress when reconnect timer fired, skipping');
      }
    }, delay);
  } else {
    console.error(`Max reconnection attempts (${MAX_RECONNECT_ATTEMPTS}) reached, waiting longer before retry`);

    // Reset after a longer delay
    setTimeout(() => {
      console.log('Resetting connection attempts counter and trying again');
      connectionAttempts = 0;
      connect();
    }, 60000); // 1 minute
  }
}

/**
 * Set up heartbeat mechanism
 */
function setupHeartbeat() {
  // Clear any existing heartbeat interval
  if (heartbeatInterval) {
    clearInterval(heartbeatInterval);
    heartbeatInterval = null;
  }

  // Set up a more aggressive heartbeat interval
  heartbeatInterval = setInterval(() => {
    if (activeConnection && activeConnection.readyState === WebSocket.OPEN) {
      // Send heartbeat
      console.log('Sending heartbeat to server');
      sendMessage({
        type: 'heartbeat',
        timestamp: Date.now()
      });

      // Check if we've received a response recently
      const now = Date.now();
      const timeSinceLastResponse = now - lastHeartbeatResponse;

      // Log heartbeat status
      console.log(`Last heartbeat response was ${Math.round(timeSinceLastResponse/1000)}s ago (timeout: ${HEARTBEAT_TIMEOUT/1000}s)`);

      // Reduced timeout from 90s to 60s to be more aggressive with reconnection
      if (timeSinceLastResponse > 60000) {
        console.warn(`No heartbeat response received for ${Math.round(timeSinceLastResponse/1000)}s, reconnecting...`);

        // Log connection state before attempting to close
        console.log(`Connection state before reconnect: ${activeConnection ? getReadyStateString(activeConnection.readyState) : 'no connection'}`);

        // Only close if the connection is still open
        if (activeConnection && activeConnection.readyState === WebSocket.OPEN) {
          try {
            // Use a cleaner close code
            activeConnection.close(1000, "Heartbeat timeout");
          } catch (e) {
            console.error('Error closing connection during heartbeat check:', e);
          }
        }

        // Wait a moment before reconnecting to avoid rapid reconnect cycles
        setTimeout(() => {
          // Force reconnect with reset attempts counter
          connectionAttempts = 0;
          connect();
        }, 1000);
      }
    } else if (!activeConnection || activeConnection.readyState !== WebSocket.OPEN) {
      // Connection is not open, attempt to reconnect
      console.log(`Heartbeat check found connection in invalid state: ${activeConnection ? getReadyStateString(activeConnection.readyState) : 'null'}`);
      forceReconnect();
    }
  }, 20000); // Send heartbeat every 20 seconds (reduced from 30s)
}

/**
 * Get a readable string for WebSocket readyState
 */
function getReadyStateString(readyState) {
  switch (readyState) {
    case WebSocket.CONNECTING: return 'CONNECTING';
    case WebSocket.OPEN: return 'OPEN';
    case WebSocket.CLOSING: return 'CLOSING';
    case WebSocket.CLOSED: return 'CLOSED';
    default: return `UNKNOWN (${readyState})`;
  }
}

/**
 * Send a message through the WebSocket
 */
export function sendMessage(message) {
  if (activeConnection && activeConnection.readyState === WebSocket.OPEN) {
    try {
      activeConnection.send(JSON.stringify(message));
      return true;
    } catch (error) {
      console.error('Error sending message:', error);
      messageQueue.push(message);
      return false;
    }
  } else {
    console.log('Connection not open, queueing message');
    messageQueue.push(message);

    // Try to connect if not already connecting
    if (!isConnecting && (!activeConnection || activeConnection.readyState !== WebSocket.CONNECTING)) {
      connect();
    }

    return false;
  }
}

/**
 * Process queued messages
 */
function processMessageQueue() {
  if (messageQueue.length === 0) {
    return;
  }

  console.log(`Processing ${messageQueue.length} queued messages`);

  // Create a copy of the queue and clear it
  const queueCopy = [...messageQueue];
  messageQueue = [];

  // Send each message
  queueCopy.forEach(message => {
    if (activeConnection && activeConnection.readyState === WebSocket.OPEN) {
      try {
        activeConnection.send(JSON.stringify(message));
      } catch (error) {
        console.error('Error sending queued message:', error);
        messageQueue.push(message); // Put it back in the queue
      }
    } else {
      messageQueue.push(message); // Put it back in the queue
    }
  });
}

/**
 * Register event callbacks
 */
export function on(event, callback) {
  if (eventCallbacks[event]) {
    eventCallbacks[event].push(callback);
  }
}

/**
 * Trigger callbacks for an event
 */
function triggerCallbacks(event, data) {
  if (eventCallbacks[event]) {
    eventCallbacks[event].forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error(`Error in ${event} callback:`, error);
      }
    });
  }
}

/**
 * Get connection status
 */
export function getConnectionStatus() {
  // Get readable state name
  let readyStateName = 'no connection';
  if (activeConnection) {
    switch (activeConnection.readyState) {
      case WebSocket.CONNECTING:
        readyStateName = 'connecting';
        break;
      case WebSocket.OPEN:
        readyStateName = 'open';
        break;
      case WebSocket.CLOSING:
        readyStateName = 'closing';
        break;
      case WebSocket.CLOSED:
        readyStateName = 'closed';
        break;
      default:
        readyStateName = `unknown (${activeConnection.readyState})`;
    }
  }

  // Calculate time since last heartbeat
  const heartbeatAge = activeConnection ? Math.round((Date.now() - lastHeartbeatResponse) / 1000) : -1;

  return {
    isConnected: activeConnection && activeConnection.readyState === WebSocket.OPEN,
    readyState: activeConnection ? activeConnection.readyState : -1,
    readyStateName: readyStateName,
    connectionAttempts,
    queuedMessages: messageQueue.length,
    isConnecting,
    lastHeartbeatAge: heartbeatAge,
    heartbeatInterval: HEARTBEAT_INTERVAL / 1000,
    heartbeatTimeout: HEARTBEAT_TIMEOUT / 1000
  };
}

/**
 * Force reconnection
 */
export function forceReconnect() {
  connectionAttempts = 0;
  connect();
}
