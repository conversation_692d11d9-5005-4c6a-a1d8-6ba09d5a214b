/**
 * Google Trends Data Downloader - Content Script
 *
 * This script adds a download button to Google Trends pages and handles
 * the interaction with the Google Trends UI to download data.
 */

// Configuration
const config = {
  // Selectors for Google Trends UI elements
  selectors: {
    // The container where we'll add our download button - multiple options to try
    menuContainers: [
      '.compare-pickers.content-wrap', // New selector for the filters bar
      '.fe-atoms-generic-header-wrapper',
      '.fe-explore-header',
      '.explore-header',
      '.widget-actions-menu',
      '.widget-actions',
      '.trends-header',
      '.trends-explore-header',
      '.trends-explore-widget-header'
    ],
    // The download menu button in Google Trends - multiple options to try
    downloadMenuButtons: [
      'button.export',
      'button.widget-actions-item[title*="Download"]',
      'button[aria-label="Download this chart"]',
      'button[aria-label="Download"]',
      'button[title="Download"]',
      '.widget-actions-item[title*="Download"]',
      '[data-tooltip="Download"]',
      // XPath-like selectors converted to CSS where possible
      'button.md-button[ng-click*="download"]',
      'button.md-button[ng-click*="export"]',
      'button.md-button[track*="Download"]'
    ],
    // The CSV download option in the dropdown menu - multiple options to try
    csvDownloadOptions: [
      'button.md-button[ng-click*="csv"]',
      'button.md-button[ng-click*="CSV"]',
      'md-menu-item button[ng-click*="csv"]',
      'md-menu-item button[ng-click*="CSV"]',
      'button[aria-label="CSV"]',
      'button[title="CSV"]',
      'a[download*=".csv"]',
      'a[href*=".csv"]',
      'button.widget-actions-menu-dropdown-item',
      '.widget-actions-menu-dropdown-item',
      // More specific selectors based on Google Trends structure
      'md-menu-content md-menu-item button',
      '.md-open-menu-container button',
      '.md-open-menu-container md-menu-item'
    ],
    // The widget container that holds charts - multiple options to try
    widgetContainers: [
      '.widget-template-component',
      '.widget',
      '.fe-line-chart',
      '.fe-geo-chart',
      '.fe-related-searches',
      '.fe-related-topics',
      '.fe-related-queries',
      '.trends-widget',
      // More specific selectors for Google Trends widgets
      'trends-widget',
      'trends-widget-line-chart',
      'trends-widget-geo-chart',
      'trends-widget-related-topics',
      'trends-widget-related-queries',
      // Angular components
      '[ng-controller*="LineChartController"]',
      '[ng-controller*="GeoMapController"]',
      '[ng-controller*="RelatedEntitiesController"]',
      '[ng-controller*="RelatedQueriesController"]'
    ],
    // The more options menu button
    moreOptionsButton: 'button[aria-label="More options"]',
    // The explore page container
    explorePage: '.explore-landing-page',
    // The trending searches page container
    trendingSearchesPage: '.trending-searches-page'
  },
  // Delay between actions (in milliseconds)
  delays: {
    afterButtonClick: 1000,
    betweenDownloads: 2000,
    initialLoad: 1000
  }
};

// State - make it accessible to window for batch download
window.state = {
  isDownloading: false,
  downloadQueue: [],
  currentDownloadIndex: 0,
  downloadedFiles: [],
  // Set to false by default to download all data
  onlyRelatedQueries: false,
  // Safety timeout to reset buttons if download process hangs
  safetyTimeout: null
};

/**
 * Main initialization function
 */
function initialize() {
  console.log('Google Trends Data Downloader: Content script loaded');

  // Wait for the page to be fully loaded
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', onPageLoaded);
  } else {
    onPageLoaded();
  }
}

/**
 * Called when the page is loaded
 */
function onPageLoaded() {
  console.log('Google Trends Data Downloader: Page loaded');

  // Add the button immediately
  addDownloadButton();

  // Also add a short delay to try again in case the page wasn't fully loaded
  setTimeout(addDownloadButton, 500);

  // And one more time after a longer delay as a fallback
  setTimeout(addDownloadButton, 1500);

  // Listen for URL changes (Google Trends is a SPA)
  observeUrlChanges();

  // Set up a periodic check to ensure our button is present
  setInterval(checkAndAddButton, 3000);
}

/**
 * Periodically checks if our button exists and adds it if not
 */
function checkAndAddButton() {
  if (!document.getElementById('gtrends-download-all-btn')) {
    console.log('Google Trends Data Downloader: Button not found, adding it');
    addDownloadButton();
  }
}

/**
 * Adds the download buttons to the Google Trends UI
 */
function addDownloadButton() {
  // Check if our buttons already exist
  if (document.getElementById('gtrends-download-all-btn')) {
    return;
  }

  console.log('Google Trends Data Downloader: Adding download buttons in fixed position');

  // Create a fixed position container that looks like it's part of the Google Trends UI
  const fixedContainer = document.createElement('div');
  fixedContainer.id = 'gtrends-fixed-container';

  // Position the container at the top of the page, similar to the Google Trends header
  fixedContainer.style.position = 'fixed';
  fixedContainer.style.top = '64px'; // Just below the Google Trends header
  fixedContainer.style.left = '0';
  fixedContainer.style.right = '0';
  fixedContainer.style.width = '100%'; // Full width

  // High z-index to ensure it's above other elements
  fixedContainer.style.zIndex = '9999';

  // Styling for the container to match Google Trends UI
  fixedContainer.style.display = 'flex';
  fixedContainer.style.flexDirection = 'row';
  fixedContainer.style.justifyContent = 'center';
  fixedContainer.style.alignItems = 'center';
  fixedContainer.style.padding = '8px 16px';
  fixedContainer.style.backgroundColor = '#fff';
  fixedContainer.style.borderBottom = '1px solid #dadce0';
  fixedContainer.style.boxShadow = '0 1px 2px rgba(0, 0, 0, 0.1)';

  // Helper function to apply button styles
  const applyButtonStyle = (button, color) => {
    // Google Material Design button styling
    button.style.padding = '0 16px';
    button.style.backgroundColor = color;
    button.style.color = 'white';
    button.style.border = 'none';
    button.style.borderRadius = '4px';
    button.style.cursor = 'pointer';
    button.style.fontFamily = 'Google Sans, Roboto, Arial, sans-serif';
    button.style.fontSize = '14px';
    button.style.fontWeight = '500';
    button.style.boxShadow = '0 1px 2px rgba(0, 0, 0, 0.3)';
    button.style.height = '36px'; // Standard Material Design button height
    button.style.minWidth = '120px'; // Reasonable minimum width
    button.style.display = 'inline-flex';
    button.style.alignItems = 'center';
    button.style.justifyContent = 'center';
    button.style.textAlign = 'center';
    button.style.transition = 'all 0.2s ease';
    button.style.margin = '0 8px';
    button.style.lineHeight = '1';

    // Hover effect
    button.onmouseover = () => {
      button.style.backgroundColor = lightenColor(color, 10);
      button.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.3)';
    };

    button.onmouseout = () => {
      button.style.backgroundColor = color;
      button.style.boxShadow = '0 1px 2px rgba(0, 0, 0, 0.3)';
    };
  };

  // Helper function to lighten a color
  const lightenColor = (color, percent) => {
    const num = parseInt(color.replace('#', ''), 16);
    const amt = Math.round(2.55 * percent);
    const R = (num >> 16) + amt;
    const G = (num >> 8 & 0x00FF) + amt;
    const B = (num & 0x0000FF) + amt;
    return '#' + (
      0x1000000 +
      (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
      (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
      (B < 255 ? B < 1 ? 0 : B : 255)
    ).toString(16).slice(1);
  };

  // Create the "Download All Data" button
  const downloadAllButton = document.createElement('button');
  downloadAllButton.id = 'gtrends-download-all-btn';
  downloadAllButton.className = 'gtrends-download-btn gtrends-fixed-btn';
  downloadAllButton.textContent = 'Download All Data';
  downloadAllButton.title = 'Download all Google Trends data from this page';
  applyButtonStyle(downloadAllButton, '#4285f4'); // Google blue

  // Add click event listener for "Download All Data"
  downloadAllButton.addEventListener('click', () => {
    console.log('Download All Data button clicked, setting onlyRelatedQueries = false');
    state.onlyRelatedQueries = false;
    onDownloadButtonClick();
  });

  // Create the "Download Related Queries" button
  const downloadRelatedQueriesButton = document.createElement('button');
  downloadRelatedQueriesButton.id = 'gtrends-download-related-queries-btn';
  downloadRelatedQueriesButton.className = 'gtrends-download-btn gtrends-fixed-btn';
  downloadRelatedQueriesButton.textContent = 'Download Related Queries';
  downloadRelatedQueriesButton.title = 'Download only related queries data from this page';
  applyButtonStyle(downloadRelatedQueriesButton, '#0F9D58'); // Google green for differentiation

  // Add click event listener for "Download Related Queries"
  downloadRelatedQueriesButton.addEventListener('click', () => {
    console.log('Download Related Queries button clicked, setting onlyRelatedQueries = true');
    state.onlyRelatedQueries = true;
    onDownloadButtonClick();
  });

  // Add the buttons to the fixed container
  fixedContainer.appendChild(downloadAllButton);
  fixedContainer.appendChild(downloadRelatedQueriesButton);

  // Add the fixed container to the body
  document.body.appendChild(fixedContainer);

  console.log('Google Trends Data Downloader: Fixed position buttons added');
}

/**
 * Handles the download button click
 */
function onDownloadButtonClick() {
  if (window.state.isDownloading) {
    console.log('Google Trends Data Downloader: Already downloading, please wait');
    return;
  }

  console.log('Google Trends Data Downloader: Download button clicked');

  // Set a timeout to ensure buttons are reset if something goes wrong
  window.state.safetyTimeout = setTimeout(() => {
    if (window.state.isDownloading) {
      console.log('Google Trends Data Downloader: Safety timeout triggered, resetting button states');
      finishDownloading();
    }
  }, 30000); // 30 seconds timeout

  // Reset state
  window.state.isDownloading = true;
  window.state.downloadQueue = [];
  window.state.currentDownloadIndex = 0;
  window.state.downloadedFiles = [];

  // Ensure onlyRelatedQueries is a proper boolean
  window.state.onlyRelatedQueries = window.state.onlyRelatedQueries === true;

  // Log the current state of onlyRelatedQueries flag
  console.log('Google Trends Data Downloader: onDownloadButtonClick - onlyRelatedQueries =', window.state.onlyRelatedQueries);

  // Only update the state of the button that was clicked
  if (window.state.onlyRelatedQueries) {
    // Update "Download Related Queries" button state only
    const downloadRelatedQueriesButton = document.getElementById('gtrends-download-related-queries-btn');
    if (downloadRelatedQueriesButton) {
      downloadRelatedQueriesButton.textContent = 'Preparing...';
      downloadRelatedQueriesButton.disabled = true;
      console.log('Google Trends Data Downloader: Set Download Related Queries button to preparing state');
    }
  } else {
    // Update "Download All Data" button state only
    const downloadAllButton = document.getElementById('gtrends-download-all-btn');
    if (downloadAllButton) {
      downloadAllButton.textContent = 'Preparing...';
      downloadAllButton.disabled = true;
      console.log('Google Trends Data Downloader: Set Download All Data button to preparing state');
    }
  }

  // Show a toast notification
  showToast('Preparing to download...', 'info');

  // Add a small delay to ensure all content is loaded before starting the download process
  setTimeout(() => {
    // Update button text to "Downloading..."
    if (window.state.onlyRelatedQueries) {
      const downloadRelatedQueriesButton = document.getElementById('gtrends-download-related-queries-btn');
      if (downloadRelatedQueriesButton) {
        downloadRelatedQueriesButton.textContent = 'Downloading...';
      }
    } else {
      const downloadAllButton = document.getElementById('gtrends-download-all-btn');
      if (downloadAllButton) {
        downloadAllButton.textContent = 'Downloading...';
      }
    }

    // First, try the direct download approach
    if (tryDirectDownload()) {
      return; // If direct download works, we're done
    }

    // If direct download fails, fall back to the widget-based approach
    console.log('Google Trends Data Downloader: Direct download failed, trying widget-based approach');

    // Find all widgets on the page using all possible selectors
    let widgets = [];

    for (const selector of config.selectors.widgetContainers) {
      const foundWidgets = document.querySelectorAll(selector);
      if (foundWidgets.length > 0) {
        console.log(`Google Trends Data Downloader: Found ${foundWidgets.length} widgets with selector: ${selector}`);
        widgets = [...widgets, ...foundWidgets];
      }
    }

    // Remove duplicates (in case multiple selectors matched the same elements)
    widgets = Array.from(new Set(widgets));

    if (widgets.length === 0) {
      console.log('Google Trends Data Downloader: No widgets found on the page');

      // Try to find download buttons directly
      let downloadButtons = [];

      for (const selector of config.selectors.downloadMenuButtons) {
        const foundButtons = document.querySelectorAll(selector);
        if (foundButtons.length > 0) {
          console.log(`Google Trends Data Downloader: Found ${foundButtons.length} download buttons with selector: ${selector}`);
          downloadButtons = [...downloadButtons, ...foundButtons];
        }
      }

      // Remove duplicates
      downloadButtons = Array.from(new Set(downloadButtons));

      if (downloadButtons.length === 0) {
        console.log('Google Trends Data Downloader: No download buttons found on the page');

        // Try one more approach - look for Angular elements
        if (tryAngularDownload()) {
          return; // If Angular download works, we're done
        }

        showToast('No downloadable content found on this page', 'error');
        finishDownloading();
        return;
      }

      // Add each download button to the queue
      downloadButtons.forEach((button, index) => {
        window.state.downloadQueue.push({
          widget: button.closest('.widget') || document.body, // Try to find parent widget or use body
          downloadButton: button,
          index,
          downloaded: false
        });
      });
    } else {
      console.log(`Google Trends Data Downloader: Found ${widgets.length} widgets`);

      // Add each widget to the download queue
      widgets.forEach((widget, index) => {
        window.state.downloadQueue.push({
          widget,
          index,
          downloaded: false
        });
      });
    }

    // Show a toast notification
    showToast(`Starting download of ${window.state.downloadQueue.length} items...`, 'info');

    // Start downloading
    processNextDownload();
  }, 1000); // 1 second delay to ensure all content is loaded
}

/**
 * Try to download CSV files directly using the export buttons
 */
function tryDirectDownload() {
  console.log('Google Trends Data Downloader: Trying direct download approach');

  // Ensure onlyRelatedQueries is a proper boolean
  window.state.onlyRelatedQueries = window.state.onlyRelatedQueries === true;

  // Log the current state of onlyRelatedQueries flag
  console.log('Google Trends Data Downloader: tryDirectDownload - onlyRelatedQueries =', window.state.onlyRelatedQueries);

  // If we're in "Download All Data" mode, try to find all export buttons first
  if (!window.state.onlyRelatedQueries) {
    // Find all export buttons (these are the download buttons in Google Trends)
    const allExportButtons = document.querySelectorAll('button.export');

    if (allExportButtons.length > 0) {
      console.log(`Google Trends Data Downloader: Found ${allExportButtons.length} export buttons for all data`);

      // Add each export button to the queue
      allExportButtons.forEach((button, index) => {
        window.state.downloadQueue.push({
          widget: button.closest('.widget') || document.body,
          downloadButton: button,
          index,
          downloaded: false,
          isExportButton: true
        });
      });

      // Show a toast notification
      showToast(`Starting download of ${window.state.downloadQueue.length} items...`, 'info');

      // Start downloading
      processNextDownload();
      return true;
    }

    // If no export buttons found, try to find all download buttons
    const allDownloadButtons = [];

    // Try each possible download button selector
    for (const selector of config.selectors.downloadMenuButtons) {
      const buttons = document.querySelectorAll(selector);
      if (buttons.length > 0) {
        console.log(`Google Trends Data Downloader: Found ${buttons.length} download buttons with selector: ${selector}`);
        buttons.forEach(button => {
          if (isElementVisible(button)) {
            allDownloadButtons.push(button);
          }
        });
      }
    }

    if (allDownloadButtons.length > 0) {
      // Remove duplicates
      const uniqueButtons = [...new Set(allDownloadButtons)];
      console.log(`Google Trends Data Downloader: Found ${uniqueButtons.length} unique download buttons for all data`);

      // Add each download button to the queue
      uniqueButtons.forEach((button, index) => {
        window.state.downloadQueue.push({
          widget: button.closest('.widget') || document.body,
          downloadButton: button,
          index,
          downloaded: false
        });
      });

      // Show a toast notification
      showToast(`Starting download of ${window.state.downloadQueue.length} items...`, 'info');

      // Start downloading
      processNextDownload();
      return true;
    }
  }

  // If we're in "Download Related Queries" mode or the above didn't find anything
  // First, try to find related queries widgets
  const relatedQueriesWidgets = document.querySelectorAll('.fe-related-queries');

  if (relatedQueriesWidgets.length > 0) {
    console.log(`Google Trends Data Downloader: Found ${relatedQueriesWidgets.length} related queries widgets`);

    // Find export buttons within related queries widgets
    let relatedQueriesExportButtons = [];

    relatedQueriesWidgets.forEach(widget => {
      const exportButton = widget.querySelector('button.export');
      if (exportButton) {
        relatedQueriesExportButtons.push({
          widget,
          button: exportButton
        });
      }
    });

    if (relatedQueriesExportButtons.length > 0) {
      console.log(`Google Trends Data Downloader: Found ${relatedQueriesExportButtons.length} export buttons in related queries widgets`);

      // Add each export button to the queue
      relatedQueriesExportButtons.forEach(({ widget, button }, index) => {
        window.state.downloadQueue.push({
          widget,
          downloadButton: button,
          index,
          downloaded: false,
          isExportButton: true,
          isRelatedQueries: true
        });
      });

      // Show a toast notification
      showToast(`Starting download of ${window.state.downloadQueue.length} related queries...`, 'info');

      // Start downloading
      processNextDownload();
      return true;
    }
  }

  // Try to find related queries using Angular-specific selectors
  const relatedQueriesControllers = document.querySelectorAll('[ng-controller*="RelatedQueriesController"]');

  if (relatedQueriesControllers.length > 0) {
    console.log(`Google Trends Data Downloader: Found ${relatedQueriesControllers.length} related queries controllers`);

    // Find export buttons within related queries controllers
    let relatedQueriesButtons = [];

    relatedQueriesControllers.forEach(controller => {
      // Look for export buttons within this controller
      const exportButton = controller.querySelector('button.export, button[title*="Download"], button[ng-click*="export"], button[ng-click*="download"]');
      if (exportButton) {
        relatedQueriesButtons.push({
          widget: controller,
          button: exportButton
        });
      }
    });

    if (relatedQueriesButtons.length > 0) {
      console.log(`Google Trends Data Downloader: Found ${relatedQueriesButtons.length} export buttons in related queries controllers`);

      // Add each export button to the queue
      relatedQueriesButtons.forEach(({ widget, button }, index) => {
        window.state.downloadQueue.push({
          widget,
          downloadButton: button,
          index,
          downloaded: false,
          isExportButton: true,
          isRelatedQueries: true
        });
      });

      // Show a toast notification
      showToast(`Starting download of ${window.state.downloadQueue.length} related queries...`, 'info');

      // Start downloading
      processNextDownload();
      return true;
    }
  }

  // If we still couldn't find related queries, try looking for specific text
  if (window.state.onlyRelatedQueries) {
    // Look for elements containing "Related queries" text
    const relatedQueriesElements = [];
    document.querySelectorAll('div, span, h1, h2, h3, h4, h5, h6').forEach(element => {
      if (element.textContent &&
          element.textContent.trim().toLowerCase() === 'related queries' &&
          isElementVisible(element)) {
        // Found a related queries heading, now look for export buttons nearby
        let parent = element;
        // Go up a few levels to find the widget container
        for (let i = 0; i < 5; i++) {
          parent = parent.parentElement;
          if (!parent) break;

          // Look for export buttons within this parent
          const exportButton = parent.querySelector('button.export, button[title*="Download"], button[ng-click*="export"], button[ng-click*="download"], button.widget-actions-item');
          if (exportButton) {
            relatedQueriesElements.push({
              widget: parent,
              button: exportButton
            });
            break;
          }
        }
      }
    });

    if (relatedQueriesElements.length > 0) {
      console.log(`Google Trends Data Downloader: Found ${relatedQueriesElements.length} related queries elements with export buttons`);

      // Add each export button to the queue
      relatedQueriesElements.forEach(({ widget, button }, index) => {
        window.state.downloadQueue.push({
          widget,
          downloadButton: button,
          index,
          downloaded: false,
          isExportButton: true,
          isRelatedQueries: true
        });
      });

      // Show a toast notification
      showToast(`Starting download of ${window.state.downloadQueue.length} related queries...`, 'info');

      // Start downloading
      processNextDownload();
      return true;
    }
  }

  // If we get here, we couldn't find any direct download buttons
  if (window.state.onlyRelatedQueries) {
    console.log('Google Trends Data Downloader: No related queries download buttons found');
  } else {
    console.log('Google Trends Data Downloader: No download buttons found');
  }

  // If we're in a state where no buttons were found but the download state is still active,
  // make sure we reset the buttons to prevent them from being stuck in "Downloading..." state
  if (window.state.isDownloading && window.state.downloadQueue.length === 0) {
    console.log('Google Trends Data Downloader: No download buttons found, resetting button states');
    finishDownloading();
  }

  return false;
}

/**
 * Try to download CSV files using Angular-specific approach
 */
function tryAngularDownload() {
  console.log('Google Trends Data Downloader: Trying Angular-specific download approach');

  // Ensure onlyRelatedQueries is a proper boolean
  window.state.onlyRelatedQueries = window.state.onlyRelatedQueries === true;

  // Log the current state of onlyRelatedQueries flag
  console.log('Google Trends Data Downloader: tryAngularDownload - onlyRelatedQueries =', window.state.onlyRelatedQueries);

  // This function will try to find and execute Angular-specific download functions
  // First, check if Angular is available
  if (!window.angular) {
    console.log('Google Trends Data Downloader: Angular not found on the page');
    return false;
  }

  // Try to find Angular controllers that might have download functions
  const controllers = [];

  // Look for elements with ng-controller attribute
  if (window.state.onlyRelatedQueries) {
    // Only look for related queries controllers
    document.querySelectorAll('[ng-controller*="RelatedQueriesController"]').forEach(element => {
      const controllerName = element.getAttribute('ng-controller');
      if (controllerName) {
        controllers.push({
          element,
          controllerName,
          isRelatedQueries: true
        });
      }
    });
  } else {
    // Look for all controllers
    document.querySelectorAll('[ng-controller]').forEach(element => {
      const controllerName = element.getAttribute('ng-controller');
      if (controllerName) {
        const isRelatedQueries = controllerName.includes('RelatedQueries');
        controllers.push({
          element,
          controllerName,
          isRelatedQueries
        });
      }
    });
  }

  if (controllers.length === 0) {
    console.log('Google Trends Data Downloader: No Angular controllers found');
    return false;
  }

  console.log(`Google Trends Data Downloader: Found ${controllers.length} Angular controllers`);

  // Try to find scope and download functions
  let foundDownloadFunction = false;

  controllers.forEach(controller => {
    try {
      const scope = angular.element(controller.element).scope();

      // Look for download or export functions in the scope
      const downloadFunctions = [];

      for (const key in scope) {
        if (typeof scope[key] === 'function' &&
            (key.toLowerCase().includes('download') ||
             key.toLowerCase().includes('export') ||
             key.toLowerCase().includes('csv'))) {
          downloadFunctions.push(key);
        }
      }

      if (downloadFunctions.length > 0) {
        console.log(`Google Trends Data Downloader: Found download functions in controller ${controller.controllerName}: ${downloadFunctions.join(', ')}`);

        // Try to call each download function
        downloadFunctions.forEach(funcName => {
          try {
            // Only call the function if:
            // 1. We're in "Download All Data" mode, or
            // 2. We're in "Download Related Queries" mode and this is a related queries controller
            if (!window.state.onlyRelatedQueries || controller.isRelatedQueries) {
              console.log(`Google Trends Data Downloader: Calling ${funcName} on ${controller.controllerName}`);
              scope[funcName]();
              foundDownloadFunction = true;
            }
          } catch (error) {
            console.log(`Google Trends Data Downloader: Error calling ${funcName}: ${error}`);
          }
        });
      }

      // Try to find the export function directly
      if (typeof scope.export === 'function') {
        // Only call the function if:
        // 1. We're in "Download All Data" mode, or
        // 2. We're in "Download Related Queries" mode and this is a related queries controller
        if (!window.state.onlyRelatedQueries || controller.isRelatedQueries) {
          try {
            console.log(`Google Trends Data Downloader: Calling export function on ${controller.controllerName}`);
            scope.export();
            foundDownloadFunction = true;
          } catch (error) {
            console.log(`Google Trends Data Downloader: Error calling export function: ${error}`);
          }
        }
      }

      // Try to find the widget object in the scope
      if (scope.ctrl && scope.ctrl.widget) {
        console.log(`Google Trends Data Downloader: Found widget in ${controller.controllerName}`);

        // Try to call widget-specific export functions
        if (scope.ctrl.widget.export && typeof scope.ctrl.widget.export === 'function') {
          // Only call the function if:
          // 1. We're in "Download All Data" mode, or
          // 2. We're in "Download Related Queries" mode and this is a related queries controller
          if (!window.state.onlyRelatedQueries || controller.isRelatedQueries) {
            try {
              console.log(`Google Trends Data Downloader: Calling widget.export function on ${controller.controllerName}`);
              scope.ctrl.widget.export();
              foundDownloadFunction = true;
            } catch (error) {
              console.log(`Google Trends Data Downloader: Error calling widget.export function: ${error}`);
            }
          }
        }
      }
    } catch (error) {
      console.log(`Google Trends Data Downloader: Error accessing scope for controller ${controller.controllerName}: ${error}`);
    }
  });

  if (foundDownloadFunction) {
    // If we found and called at least one download function, consider it a success
    if (window.state.onlyRelatedQueries) {
      showToast('Download of related queries initiated through Angular', 'success');
    } else {
      showToast('Download initiated through Angular', 'success');
    }
    finishDownloading();
    return true;
  }

  // If we're in a state where no download functions were found but the download state is still active,
  // make sure we reset the buttons to prevent them from being stuck in "Downloading..." state
  if (window.state.isDownloading && window.state.downloadQueue.length === 0) {
    console.log('Google Trends Data Downloader: No Angular download functions found, resetting button states');
    finishDownloading();
  }

  return false;
}

/**
 * Shows a toast notification
 */
function showToast(message, type = 'info') {
  // Remove any existing toast
  const existingToast = document.getElementById('gtrends-toast');
  if (existingToast) {
    existingToast.remove();
  }

  // Create a new toast
  const toast = document.createElement('div');
  toast.id = 'gtrends-toast';
  toast.className = `gtrends-toast ${type}`;
  toast.textContent = message;

  // Apply modern styling to the toast
  toast.style.position = 'fixed';
  toast.style.bottom = '20px';
  toast.style.left = '50%';
  toast.style.transform = 'translateX(-50%)';
  toast.style.backgroundColor = type === 'error' ? '#EA4335' :
                               type === 'success' ? '#0F9D58' :
                               '#4285F4'; // Google colors
  toast.style.color = 'white';
  toast.style.padding = '12px 24px';
  toast.style.borderRadius = '8px';
  toast.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
  toast.style.zIndex = '10000';
  toast.style.fontFamily = 'Google Sans, Roboto, Arial, sans-serif';
  toast.style.fontSize = '14px';
  toast.style.fontWeight = '500';
  toast.style.opacity = '0';
  toast.style.transition = 'opacity 0.3s ease-in-out';

  // Add the toast to the body
  document.body.appendChild(toast);

  // Show the toast
  setTimeout(() => {
    toast.style.opacity = '1';
  }, 10);

  // Hide the toast after 3 seconds
  setTimeout(() => {
    toast.style.opacity = '0';
    setTimeout(() => {
      toast.remove();
    }, 300);
  }, 3000);
}

/**
 * Processes the next download in the queue
 */
function processNextDownload() {
  if (window.state.currentDownloadIndex >= window.state.downloadQueue.length) {
    finishDownloading();
    return;
  }

  const downloadItem = window.state.downloadQueue[window.state.currentDownloadIndex];
  console.log(`Google Trends Data Downloader: Processing download ${window.state.currentDownloadIndex + 1}/${window.state.downloadQueue.length}`);

  // Update progress in the UI
  showToast(`Downloading ${window.state.currentDownloadIndex + 1}/${window.state.downloadQueue.length}...`, 'info');

  // Check if this is an export button (direct download)
  if (downloadItem.isExportButton) {
    console.log(`Google Trends Data Downloader: Clicking export button for item ${downloadItem.index}`);

    // Click the export button directly
    downloadItem.downloadButton.click();

    // Mark this download as complete
    downloadItem.downloaded = true;

    // Add to downloaded files list
    window.state.downloadedFiles.push({
      widgetIndex: downloadItem.index,
      timestamp: new Date().toISOString(),
      type: 'direct_export'
    });

    // Move to the next download after a delay
    window.state.currentDownloadIndex++;
    setTimeout(processNextDownload, config.delays.betweenDownloads);
    return;
  }

  // Use the pre-found download button if available
  let downloadMenuButton = downloadItem.downloadButton;

  // If not available, try to find it in the widget
  if (!downloadMenuButton) {
    // Try each possible download button selector
    for (const selector of config.selectors.downloadMenuButtons) {
      // First try to find it within the widget
      let button = downloadItem.widget.querySelector(selector);

      // If not found in the widget, try to find it in the document
      if (!button && selector.includes('md-button')) {
        // For Angular buttons, we might need to search the entire document
        const buttons = document.querySelectorAll(selector);
        if (buttons.length > 0) {
          // Use the first visible button
          for (const btn of buttons) {
            if (isElementVisible(btn)) {
              button = btn;
              break;
            }
          }
        }
      }

      if (button) {
        downloadMenuButton = button;
        console.log(`Google Trends Data Downloader: Found download button with selector: ${selector}`);
        break;
      }
    }
  }

  if (!downloadMenuButton) {
    console.log(`Google Trends Data Downloader: No download button found for widget ${downloadItem.index}`);
    state.currentDownloadIndex++;
    setTimeout(processNextDownload, config.delays.betweenDownloads);
    return;
  }

  // Click the download menu button
  downloadMenuButton.click();
  console.log(`Google Trends Data Downloader: Clicked download button for widget ${downloadItem.index}`);

  // Wait for the dropdown menu to appear
  setTimeout(() => {
    // Try each possible CSV download option selector
    let csvDownloadOption = null;

    for (const selector of config.selectors.csvDownloadOptions) {
      // First try to find it within the widget
      let option = downloadItem.widget.querySelector(selector);

      // If not found in the widget, try to find it in the document
      // (dropdown menus are often appended to the body, not the widget)
      if (!option) {
        const options = document.querySelectorAll(selector);
        if (options.length > 0) {
          // Use the first visible option
          for (const opt of options) {
            if (isElementVisible(opt)) {
              option = opt;
              break;
            }
          }
        }
      }

      if (option) {
        csvDownloadOption = option;
        console.log(`Google Trends Data Downloader: Found CSV option with selector: ${selector}`);
        break;
      }
    }

    // If no CSV option is found, try to find any menu item with "CSV" text
    if (!csvDownloadOption) {
      // Look for any element containing "CSV" text
      const elements = document.querySelectorAll('button, a, div, span');
      for (const element of elements) {
        if (element.textContent &&
            element.textContent.toUpperCase().includes('CSV') &&
            isElementVisible(element)) {
          csvDownloadOption = element;
          console.log('Google Trends Data Downloader: Found CSV option by text content');
          break;
        }
      }
    }

    if (!csvDownloadOption) {
      console.log(`Google Trends Data Downloader: No CSV option found for widget ${downloadItem.index}`);

      // Try clicking any visible dropdown option as a fallback
      const anyOption = document.querySelector('.widget-actions-menu-dropdown-item:not([style*="display: none"]), md-menu-item:not([style*="display: none"]) button');
      if (anyOption) {
        console.log(`Google Trends Data Downloader: Trying to click any visible dropdown option as fallback`);
        anyOption.click();
        downloadItem.downloaded = true;
      } else {
        // If no option is found, try clicking the download button again to close the menu
        downloadMenuButton.click();
      }

      state.currentDownloadIndex++;
      setTimeout(processNextDownload, config.delays.betweenDownloads);
      return;
    }

    // Click the CSV download option
    csvDownloadOption.click();
    console.log(`Google Trends Data Downloader: Clicked CSV option for widget ${downloadItem.index}`);

    // Mark this download as complete
    downloadItem.downloaded = true;

    // Add to downloaded files list (we don't know the filename yet)
    window.state.downloadedFiles.push({
      widgetIndex: downloadItem.index,
      timestamp: new Date().toISOString()
    });

    // Move to the next download
    window.state.currentDownloadIndex++;
    setTimeout(processNextDownload, config.delays.betweenDownloads);
  }, config.delays.afterButtonClick);
}

/**
 * Check if an element is visible
 */
function isElementVisible(element) {
  if (!element) return false;

  const style = window.getComputedStyle(element);
  return style.display !== 'none' &&
         style.visibility !== 'hidden' &&
         style.opacity !== '0' &&
         element.offsetWidth > 0 &&
         element.offsetHeight > 0;
}

/**
 * Finishes the downloading process
 */
function finishDownloading() {
  // Clear any safety timeout
  if (window.state.safetyTimeout) {
    clearTimeout(window.state.safetyTimeout);
    window.state.safetyTimeout = null;
  }

  window.state.isDownloading = false;

  // Only reset the button that was clicked
  if (window.state.onlyRelatedQueries) {
    // Reset "Download Related Queries" button state only
    const downloadRelatedQueriesButton = document.getElementById('gtrends-download-related-queries-btn');
    if (downloadRelatedQueriesButton) {
      downloadRelatedQueriesButton.textContent = 'Download Related Queries';
      downloadRelatedQueriesButton.disabled = false;
      console.log('Google Trends Data Downloader: Reset Download Related Queries button');
    }
  } else {
    // Reset "Download All Data" button state only
    const downloadAllButton = document.getElementById('gtrends-download-all-btn');
    if (downloadAllButton) {
      downloadAllButton.textContent = 'Download All Data';
      downloadAllButton.disabled = false;
      console.log('Google Trends Data Downloader: Reset Download All Data button');
    }
  }

  // Count successful downloads
  const successfulDownloads = window.state.downloadQueue.filter(item => item.downloaded).length;

  console.log(`Google Trends Data Downloader: Finished downloading ${successfulDownloads}/${window.state.downloadQueue.length} files`);

  // Show a toast notification
  if (successfulDownloads > 0) {
    showToast(`Successfully downloaded ${successfulDownloads} of ${window.state.downloadQueue.length} files`, 'success');
  } else {
    showToast(`No files were downloaded. Please try again.`, 'error');
  }

  // Send message to background script with clear indication to close the tab
  chrome.runtime.sendMessage({
    action: 'downloadComplete', // Changed to match the handler in background.js
    requestId: new URLSearchParams(window.location.search).get('requestId') || Date.now().toString(),
    fileName: window.state.downloadedFiles.length > 0 ?
              `Downloaded ${successfulDownloads} files` :
              'unknown',
    tabId: chrome.runtime.sendMessage({ action: 'getTabId' }, (response) => {
      console.log('Got tab ID:', response ? response.tabId : 'unknown');
    }),
    stats: {
      totalWidgets: window.state.downloadQueue.length,
      successfulDownloads,
      downloadedFiles: window.state.downloadedFiles,
    },
    timestamp: Date.now()
  }, response => {
    console.log('Google Trends Data Downloader: Received response from background script:', response);

    // Log that we're waiting for the tab to be closed
    console.log('Google Trends Data Downloader: Waiting for tab to be closed by background script...');

    // As a fallback, also send the old message format for compatibility
    chrome.runtime.sendMessage({
      action: 'downloadsComplete',
      data: {
        totalWidgets: window.state.downloadQueue.length,
        successfulDownloads,
        downloadedFiles: window.state.downloadedFiles,
        closeTab: true, // Explicitly indicate that the tab should be closed
        timestamp: Date.now()
      }
    });
  });
}

/**
 * Observes URL changes to re-add the download button when navigating
 */
function observeUrlChanges() {
  let lastUrl = location.href;

  // Create a new observer
  const observer = new MutationObserver(() => {
    if (location.href !== lastUrl) {
      lastUrl = location.href;
      console.log('Google Trends Data Downloader: URL changed, re-adding download button');

      // Try to add the button immediately
      addDownloadButton();

      // Also set up a short delay to try again
      setTimeout(addDownloadButton, 500);

      // And one more time after a longer delay as a fallback
      setTimeout(addDownloadButton, 1500);
    }
  });

  // Start observing
  observer.observe(document, { subtree: true, childList: true });
}

// Add a function to log the DOM structure for debugging
function logDOMStructure() {
  console.log('-- IN SELF ON CONTEXT --');

  // Log all buttons
  const buttons = document.querySelectorAll('button');
  console.log('Constructing result arrays');

  const buttonInfo = [];
  buttons.forEach(button => {
    const text = button.textContent && button.textContent.trim();
    const classes = button.className;
    const attributes = {};

    // Get all attributes
    for (let i = 0; i < button.attributes.length; i++) {
      const attr = button.attributes[i];
      attributes[attr.name] = attr.value;
    }

    buttonInfo.push({
      text,
      classes,
      attributes,
      visible: isElementVisible(button)
    });
  });

  console.log(`Result array length: ${buttonInfo.length}`);

  // Look for the compare-pickers container
  findComparePickersContainer();
}

// Function to find the compare-pickers container
function findComparePickersContainer() {
  console.log('Searching for compare-pickers container...');

  // Try various selectors
  const selectors = [
    '.compare-pickers.content-wrap',
    '.compare-pickers',
    'div.compare-pickers',
    '[class*="compare-pickers"]',
    '[class*="content-wrap"]',
    'div.content-wrap',
    'hierarchy-picker',
    'custom-date-picker',
    'md-select'
  ];

  // Also look for specific elements from the HTML structure
  const hierarchyPickers = document.querySelectorAll('hierarchy-picker');
  const customDatePickers = document.querySelectorAll('custom-date-picker');
  const mdSelects = document.querySelectorAll('md-select');

  console.log('Found hierarchy-pickers:', hierarchyPickers.length);
  console.log('Found custom-date-pickers:', customDatePickers.length);
  console.log('Found md-selects:', mdSelects.length);

  // If we found these elements, try to find their common parent
  if (hierarchyPickers.length > 0 && customDatePickers.length > 0) {
    // Look for a common parent that might be the compare-pickers container
    const hierarchyParent = hierarchyPickers[0].parentElement;
    const datePickerParent = customDatePickers[0].parentElement;

    console.log('Hierarchy picker parent:', hierarchyParent ? hierarchyParent.tagName : 'none');
    console.log('Date picker parent:', datePickerParent ? datePickerParent.tagName : 'none');

    if (hierarchyParent && hierarchyParent === datePickerParent) {
      console.log('Found common parent for pickers');
      console.log('Common parent classes:', hierarchyParent.className);
      console.log('Common parent HTML:', hierarchyParent.outerHTML.substring(0, 200));
    }
  }

  for (const selector of selectors) {
    const elements = document.querySelectorAll(selector);
    console.log(`Selector "${selector}" found ${elements.length} elements`);

    if (elements.length > 0) {
      // Log the first element's details
      const element = elements[0];
      console.log(`First element classes: ${element.className}`);
      console.log(`First element tag: ${element.tagName}`);
      console.log(`First element parent: ${element.parentElement ? element.parentElement.tagName : 'none'}`);
      console.log(`First element parent classes: ${element.parentElement ? element.parentElement.className : 'none'}`);

      // Try to find a more specific selector
      let parent = element.parentElement;
      let specificSelector = '';

      if (parent) {
        if (parent.id) {
          specificSelector = `#${parent.id} > ${element.tagName.toLowerCase()}${element.className ? '.' + element.className.replace(/\s+/g, '.') : ''}`;
        } else if (parent.className) {
          specificSelector = `.${parent.className.replace(/\s+/g, '.')} > ${element.tagName.toLowerCase()}${element.className ? '.' + element.className.replace(/\s+/g, '.') : ''}`;
        }

        if (specificSelector) {
          console.log(`Specific selector: ${specificSelector}`);
          console.log(`Elements matching specific selector: ${document.querySelectorAll(specificSelector).length}`);
        }
      }
    }
  }

  // Look for elements with specific text content
  const elementsWithText = [];
  document.querySelectorAll('div').forEach(div => {
    if (div.textContent && (
        div.textContent.includes('United States') ||
        div.textContent.includes('Past day') ||
        div.textContent.includes('All categories'))) {
      elementsWithText.push({
        element: div,
        text: div.textContent.trim().substring(0, 30),
        classes: div.className
      });
    }
  });

  console.log('Elements with relevant text content:', elementsWithText.length);
  elementsWithText.forEach((item, index) => {
    console.log(`Element ${index}: "${item.text}" - Classes: ${item.classes}`);
  });
}

// Call logDOMStructure periodically to help with debugging
setInterval(logDOMStructure, 10000);

// Initialize the content script
initialize();
