/**
 * Google Trends Data Downloader - Popup Script
 *
 * This script handles the popup UI and interactions.
 */

// DOM Elements
const elements = {
  statusMessage: document.getElementById('status-message'),

  // Batch download elements
  urlInput: document.getElementById('url-input'),
  addUrlBtn: document.getElementById('add-url-btn'),
  urlList: document.getElementById('url-list'),
  emptyListMessage: document.getElementById('empty-list-message'),
  savedUrlLists: document.getElementById('saved-url-lists'),
  createListBtn: document.getElementById('create-list-btn'),
  deleteListBtn: document.getElementById('delete-list-btn'),
  batchDownloadAllBtn: document.getElementById('batch-download-all-btn'),
  batchDownloadRelatedBtn: document.getElementById('batch-download-related-btn'),
  batchDownloadRelatedBtnFooter: document.getElementById('batch-download-related-btn-footer'),
  batchProgress: document.getElementById('batch-progress'),
  progressFill: document.getElementById('progress-fill'),
  progressText: document.getElementById('progress-text'),
  currentUrl: document.getElementById('current-url'),
  cancelBatchBtn: document.getElementById('cancel-batch-btn')
};

// Store the current list of URLs
let currentUrls = [];

/**
 * Initialize the popup
 */
function initialize() {
  console.log('Google Trends Data Downloader: Popup initialized');

  // Initialize with debug logging
  console.log('Initializing event listeners and UI');

  // Add event listeners for URL management
  elements.addUrlBtn.addEventListener('click', addUrl);
  elements.urlInput.addEventListener('keypress', (e) => {
    if (e.key === 'Enter') {
      addUrl();
      e.preventDefault();
    }
  });

  // Add event listeners for batch download functionality
  // Add change event listener to the dropdown
  elements.savedUrlLists.addEventListener('change', function() {
    console.log('Dropdown change event triggered');
    onUrlListSelected();
  });
  elements.createListBtn.addEventListener('click', createList);
  elements.deleteListBtn.addEventListener('click', deleteUrlList);
  elements.batchDownloadAllBtn.addEventListener('click', () => startBatchDownload(false));
  elements.batchDownloadRelatedBtn.addEventListener('click', () => startBatchDownload(true));
  elements.batchDownloadRelatedBtnFooter.addEventListener('click', () => startBatchDownload(true));
  elements.cancelBatchBtn.addEventListener('click', cancelBatchDownload);

  // No need to disable any buttons initially

  // Listen for messages from background script
  chrome.runtime.onMessage.addListener(handleMessage);

  // Load the current state
  loadState();

  // Load saved URL lists
  loadSavedUrlLists();

  // Initialize the URL list UI
  updateUrlListUI();

  // Check connection status with the Node.js server
  checkNodeJsConnection();
}

/**
 * Check the connection status with the Node.js server
 */
function checkNodeJsConnection() {
  chrome.runtime.sendMessage({ action: 'checkConnectionStatus' }, (response) => {
    if (chrome.runtime.lastError) {
      console.error('Error checking connection status:', chrome.runtime.lastError);
      updateStatusMessage('Error checking Node.js connection', 'error');
      return;
    }

    if (response && response.isConnected) {
      console.log('Node.js connection is active:', response.details);
      updateStatusMessage('Connected to Node.js server', 'success');
    } else {
      console.log('Node.js connection is down:', response ? response.details : 'No response');
      updateStatusMessage('Not connected to Node.js server', 'error');
    }
  });
}

/**
 * Load the current state from storage
 */
function loadState() {
  // Get the last download information and current selected list
  chrome.storage.local.get(['lastDownload', 'currentSelectedList'], (result) => {
    if (result.lastDownload) {
      updateStatusMessage(`Last download: ${formatDate(result.lastDownload.timestamp)}`, 'success');
    }

    // Restore the previously selected list if available
    if (result.currentSelectedList) {
      // We'll set this after the lists are loaded in loadSavedUrlLists
      window.previouslySelectedList = result.currentSelectedList;
    }
  });

  // Check if we're on a Google Trends page
  chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
    const currentTab = tabs[0];

    if (currentTab && currentTab.url && currentTab.url.includes('trends.google.com')) {
      updateStatusMessage('Ready to download data from this Google Trends page');
    } else {
      updateStatusMessage('Navigate to Google Trends to download data');
    }
  });
}

/**
 * Handle messages from background script
 */
function handleMessage(message, sender, sendResponse) {
  console.log('Google Trends Data Downloader: Popup received message', message);

  if (message.action === 'updateDownloadStatus') {
    const { totalWidgets, successfulDownloads } = message.data;
    updateStatusMessage(`Downloaded ${successfulDownloads} of ${totalWidgets} widgets`, 'success');
  }

  if (message.action === 'downloadStarted') {
    updateStatusMessage(`Downloading: ${message.data.filename}`);
  }

  if (message.action === 'downloadCompleted') {
    updateStatusMessage(`Download complete: ${message.data.filename}`, 'success');
  }

  if (message.action === 'downloadFailed') {
    updateStatusMessage(`Download failed: ${message.data.error}`, 'error');
  }

  // Batch download messages
  if (message.action === 'batchDownloadProgress') {
    updateBatchProgress(message.data);
  }

  if (message.action === 'batchDownloadComplete') {
    finishBatchDownload(message.data);
  }

  if (message.action === 'batchDownloadError') {
    updateStatusMessage(`Batch download error: ${message.data.error}`, 'error');
    resetBatchDownloadUI();
  }
}

/**
 * Update the status message
 */
function updateStatusMessage(message, type = '') {
  elements.statusMessage.textContent = message;

  // Reset classes
  elements.statusMessage.className = '';

  // Add type class if provided
  if (type) {
    elements.statusMessage.classList.add(type);
  }
}



/**
 * Format a date string
 */
function formatDate(dateString) {
  const date = new Date(dateString);
  return date.toLocaleString();
}

// These functions have been removed as the buttons were removed from the UI

/**
 * Load saved URL lists from storage
 */
function loadSavedUrlLists() {
  console.log('Loading saved URL lists');
  chrome.storage.local.get('urlLists', (result) => {
    console.log('Retrieved URL lists from storage:', result.urlLists);

    // Clear the select options except for the default one
    while (elements.savedUrlLists.options.length > 1) {
      elements.savedUrlLists.remove(1);
    }

    if (result.urlLists && Object.keys(result.urlLists).length > 0) {
      console.log('Found URL lists:', Object.keys(result.urlLists));

      // Add each saved list to the select dropdown
      for (const listName in result.urlLists) {
        const option = document.createElement('option');
        option.value = listName;
        option.textContent = listName;
        elements.savedUrlLists.appendChild(option);
        console.log('Added option:', listName);
      }

      elements.deleteListBtn.disabled = false;

      // Restore the previously selected list if available
      if (window.previouslySelectedList) {
        console.log('Attempting to restore previously selected list:', window.previouslySelectedList);

        // Check if the previously selected list still exists
        const listExists = Array.from(elements.savedUrlLists.options).some(
          option => option.value === window.previouslySelectedList
        );

        if (listExists) {
          console.log('Previously selected list exists, restoring it');
          elements.savedUrlLists.value = window.previouslySelectedList;

          // Trigger the change event to load the list content
          console.log('Manually triggering onUrlListSelected');
          onUrlListSelected();
        } else {
          console.log('Previously selected list no longer exists');
        }

        // Clear the temporary variable
        delete window.previouslySelectedList;
      } else {
        console.log('No previously selected list to restore');
      }
    } else {
      elements.deleteListBtn.disabled = true;
    }
  });
}

/**
 * Handle URL list selection
 */
function onUrlListSelected() {
  const selectedList = elements.savedUrlLists.value;
  console.log('List selected:', selectedList);

  if (!selectedList) {
    // Clear the URL list if "-- Select a saved list --" is selected
    currentUrls = [];
    updateUrlListUI();
    updateStatusMessage('No list selected', 'info');
    return;
  }

  // Load the selected list
  chrome.storage.local.get('urlLists', (result) => {
    console.log('Loading list data:', result.urlLists ? result.urlLists[selectedList] : 'No data found');

    if (result.urlLists && result.urlLists[selectedList]) {
      // Update the current URLs array
      currentUrls = [...result.urlLists[selectedList]];
      console.log('Loaded URLs:', currentUrls);

      // Update the UI
      updateUrlListUI();

      // Store the currently selected list name in storage
      chrome.storage.local.set({ 'currentSelectedList': selectedList });

      updateStatusMessage(`Loaded list "${selectedList}" with ${currentUrls.length} URLs`, 'success');
    } else {
      updateStatusMessage(`Error: Could not load list "${selectedList}"`, 'error');
    }
  });
}

/**
 * Create a new URL list
 */
function createList() {
  console.log('Create button clicked');
  console.log('Current URLs:', currentUrls);

  // Allow creating empty lists - user can add URLs later with the + button
  console.log('Creating list, current URL count:', currentUrls.length);

  // Always create a new list
  console.log('Creating a new list');

  // Prompt for a name for the list
  const listName = prompt('Enter a name for this URL list:');
  console.log('User entered list name:', listName);

  if (!listName) {
    console.log('User cancelled list creation');
    return; // User cancelled
  }

  // Save the list
  chrome.storage.local.get('urlLists', (result) => {
    console.log('Retrieved existing URL lists:', result.urlLists);

    const urlLists = result.urlLists || {};
    console.log('URL lists after initialization:', urlLists);

    // Check if the list name already exists
    if (urlLists[listName]) {
      console.log('List name already exists, asking for confirmation');
      if (!confirm(`A list named "${listName}" already exists. Do you want to overwrite it?`)) {
        console.log('User cancelled overwrite');
        return; // User cancelled overwrite
      }
      console.log('User confirmed overwrite');
    }

    console.log('Setting list content:', currentUrls);
    urlLists[listName] = [...currentUrls]; // Create a copy to avoid reference issues

    console.log('Saving new list to storage:', urlLists);
    chrome.storage.local.set({ urlLists }, () => {
      console.log('List saved successfully');
      updateStatusMessage(`URL list "${listName}" saved successfully`, 'success');

      console.log('Reloading saved URL lists');
      loadSavedUrlLists();

      // Select the newly saved list
      console.log('Setting dropdown value to new list:', listName);
      setTimeout(() => {
        elements.savedUrlLists.value = listName;
        console.log('Dropdown value after setting:', elements.savedUrlLists.value);

        // Manually trigger the URL list selection to load the URLs
        console.log('Triggering onUrlListSelected');
        onUrlListSelected();

        // Disable batch download buttons if the list is empty
        if (currentUrls.length === 0) {
          console.log('Disabling batch download buttons for empty list');
          elements.batchDownloadAllBtn.disabled = true;
          elements.batchDownloadRelatedBtn.disabled = true;
          elements.batchDownloadRelatedBtnFooter.disabled = true;
          updateStatusMessage('List created. Add URLs using the + button', 'info');
        }
      }, 100); // Small delay to ensure the dropdown has been updated
    });
  });
}

/**
 * Delete the selected URL list
 */
function deleteUrlList() {
  const selectedList = elements.savedUrlLists.value;

  if (!selectedList) {
    updateStatusMessage('Please select a list to delete', 'error');
    return;
  }

  // Confirm deletion
  if (!confirm(`Are you sure you want to delete the list "${selectedList}"?`)) {
    return;
  }

  // Delete the list
  chrome.storage.local.get('urlLists', (result) => {
    if (result.urlLists && result.urlLists[selectedList]) {
      delete result.urlLists[selectedList];

      chrome.storage.local.set({ urlLists: result.urlLists }, () => {
        updateStatusMessage(`URL list "${selectedList}" deleted`, 'success');
        loadSavedUrlLists();
        // Clear the current URLs
        currentUrls = [];
        updateUrlListUI();
      });
    }
  });
}

/**
 * Add a URL to the list
 */
function addUrl() {
  const url = elements.urlInput.value.trim();

  if (!url) {
    updateStatusMessage('Please enter a URL', 'error');
    return;
  }

  if (!url.startsWith('https://trends.google.com')) {
    updateStatusMessage('Please enter a valid Google Trends URL', 'error');
    return;
  }

  // Check if URL already exists in the list
  if (currentUrls.includes(url)) {
    updateStatusMessage('This URL is already in the list', 'error');
    return;
  }

  // Add the URL to the list
  currentUrls.push(url);

  // Clear the input field
  elements.urlInput.value = '';

  // Update the UI
  updateUrlListUI();

  // Focus the input field for the next URL
  elements.urlInput.focus();

  // If a list is selected, update it in storage
  const selectedList = elements.savedUrlLists.value;
  if (selectedList) {
    updateListInStorage(selectedList);
  }

  updateStatusMessage('URL added to the list', 'success');
}

/**
 * Update the current list in storage
 */
function updateListInStorage(listName) {
  console.log('Updating list in storage:', listName);

  chrome.storage.local.get('urlLists', (result) => {
    if (result.urlLists) {
      // Update the list with current URLs
      result.urlLists[listName] = [...currentUrls];

      // Save back to storage
      chrome.storage.local.set({ urlLists: result.urlLists }, () => {
        console.log('List updated in storage:', listName);
      });
    }
  });
}

/**
 * Remove a URL from the list
 */
function removeUrl(index) {
  // Remove the URL from the array
  currentUrls.splice(index, 1);

  // Update the UI
  updateUrlListUI();

  // If a list is selected, update it in storage
  const selectedList = elements.savedUrlLists.value;
  if (selectedList) {
    updateListInStorage(selectedList);
  }

  updateStatusMessage('URL removed from the list', 'success');
}

/**
 * Extract keywords from a Google Trends URL
 */
function extractKeywordsFromUrl(url) {
  try {
    // Create a URL object to parse the URL
    const urlObj = new URL(url);

    // Get the query parameters
    const params = new URLSearchParams(urlObj.search);

    // Extract the q parameter which contains the keywords
    const queryParam = params.get('q');

    if (!queryParam) {
      return 'No keywords found';
    }

    // Split by comma if there are multiple comparison terms
    const terms = queryParam.split(',');

    // Process each term to make it more readable
    const processedTerms = terms.map(term => {
      // Replace URL encoding
      let decoded = decodeURIComponent(term);

      // Replace + with spaces
      decoded = decoded.replace(/\+/g, ' ');

      // If the term is too long, truncate it
      if (decoded.length > 40) {
        decoded = decoded.substring(0, 37) + '...';
      }

      return decoded;
    });

    // Join the terms with a separator
    return processedTerms.join(' vs ');
  } catch (error) {
    console.error('Error extracting keywords:', error);
    return 'Invalid URL format';
  }
}

/**
 * Update the URL list UI
 */
function updateUrlListUI() {
  // Clear the current list
  elements.urlList.innerHTML = '';

  // Show/hide the empty list message
  if (currentUrls.length === 0) {
    elements.emptyListMessage.style.display = 'block';
    elements.batchDownloadAllBtn.disabled = true;
    elements.batchDownloadRelatedBtn.disabled = true;
    elements.batchDownloadRelatedBtnFooter.disabled = true;
  } else {
    elements.emptyListMessage.style.display = 'none';
    elements.batchDownloadAllBtn.disabled = false;
    elements.batchDownloadRelatedBtn.disabled = false;
    elements.batchDownloadRelatedBtnFooter.disabled = false;
  }

  // Add each URL to the list
  currentUrls.forEach((url, index) => {
    const urlItem = document.createElement('div');
    urlItem.className = 'url-item';

    // Extract keywords from the URL
    const keywords = extractKeywordsFromUrl(url);

    // Create the URL text element as a clickable link
    const urlText = document.createElement('div');
    urlText.className = 'url-text clickable';
    urlText.textContent = keywords;

    // Add tooltip with full URL
    urlText.title = "Click to open: " + url;

    // Add click event to open the URL in a new tab
    urlText.addEventListener('click', () => {
      chrome.tabs.create({ url: url });
    });

    // Create the remove button
    const removeButton = document.createElement('button');
    removeButton.className = 'icon-button remove';
    removeButton.textContent = '−';
    removeButton.title = 'Remove URL';
    removeButton.addEventListener('click', () => removeUrl(index));

    // Add elements to the item
    urlItem.appendChild(urlText);
    urlItem.appendChild(removeButton);

    // Add the item to the list
    elements.urlList.appendChild(urlItem);
  });
}

/**
 * Parse URLs from textarea input (for backward compatibility)
 */
function parseUrls(input) {
  if (!input.trim()) {
    return [];
  }

  // Split by newlines and filter out empty lines
  return input.split(/\r?\n/)
    .map(url => url.trim())
    .filter(url => url && url.startsWith('https://trends.google.com'));
}

/**
 * Start a batch download
 */
function startBatchDownload(onlyRelatedQueries) {
  if (currentUrls.length === 0) {
    updateStatusMessage('Please add at least one valid Google Trends URL', 'error');
    return;
  }

  // Show the progress UI
  elements.batchProgress.classList.remove('hidden');
  elements.progressFill.style.width = '0%';
  elements.progressText.textContent = `0/${currentUrls.length} URLs processed`;
  elements.currentUrl.textContent = 'Preparing...';

  // Disable the batch download buttons
  elements.batchDownloadAllBtn.disabled = true;
  elements.batchDownloadRelatedBtn.disabled = true;
  elements.batchDownloadRelatedBtnFooter.disabled = true;
  elements.createListBtn.disabled = true;
  elements.deleteListBtn.disabled = true;
  elements.savedUrlLists.disabled = true;
  elements.addUrlBtn.disabled = true;
  elements.urlInput.disabled = true;

  // Disable all remove buttons
  const removeButtons = document.querySelectorAll('.icon-button.remove');
  removeButtons.forEach(button => {
    button.disabled = true;
  });

  // Send the batch download request to the background script
  chrome.runtime.sendMessage({
    action: 'startBatchDownload',
    data: {
      urls: currentUrls,
      onlyRelatedQueries
    }
  });

  updateStatusMessage(`Starting batch download of ${currentUrls.length} URLs...`, 'info');
}

/**
 * Update the batch download progress UI
 */
function updateBatchProgress(data) {
  const { currentIndex, totalUrls, currentUrl } = data;
  const progress = Math.round((currentIndex / totalUrls) * 100);

  elements.progressFill.style.width = `${progress}%`;
  elements.progressText.textContent = `${currentIndex}/${totalUrls} URLs processed`;

  // Extract keywords from the current URL
  const keywords = extractKeywordsFromUrl(currentUrl);

  // Set the keywords as the text and the full URL as the title
  elements.currentUrl.textContent = keywords;
  elements.currentUrl.title = "Click to open: " + currentUrl;

  // Make it clickable - remove any existing click listeners first
  elements.currentUrl.removeEventListener('click', elements.currentUrl.clickHandler);

  // Create and store the click handler
  elements.currentUrl.clickHandler = () => {
    chrome.tabs.create({ url: currentUrl });
  };

  // Add the click event listener
  elements.currentUrl.addEventListener('click', elements.currentUrl.clickHandler);
}

/**
 * Finish the batch download process
 */
function finishBatchDownload(data) {
  const { totalUrls, successfulUrls } = data;

  updateStatusMessage(`Batch download complete: ${successfulUrls} of ${totalUrls} URLs processed successfully`, 'success');
  resetBatchDownloadUI();
}

/**
 * Cancel the current batch download
 */
function cancelBatchDownload() {
  chrome.runtime.sendMessage({
    action: 'cancelBatchDownload'
  });

  updateStatusMessage('Batch download cancelled', 'info');
  resetBatchDownloadUI();
}

/**
 * Reset the batch download UI
 */
function resetBatchDownloadUI() {
  elements.batchProgress.classList.add('hidden');
  elements.batchDownloadAllBtn.disabled = currentUrls.length === 0;
  elements.batchDownloadRelatedBtn.disabled = currentUrls.length === 0;
  elements.batchDownloadRelatedBtnFooter.disabled = currentUrls.length === 0;
  elements.createListBtn.disabled = false;
  elements.addUrlBtn.disabled = false;
  elements.urlInput.disabled = false;

  // Only enable delete button if a list is selected
  const selectedList = elements.savedUrlLists.value;
  elements.deleteListBtn.disabled = !selectedList;

  elements.savedUrlLists.disabled = false;

  // Re-enable all remove buttons
  updateUrlListUI();
}

// Initialize the popup when the DOM is loaded
document.addEventListener('DOMContentLoaded', initialize);
