/**
 * Node.js Integration Module
 * Handles communication between the Chrome extension and a Node.js server
 */

// Configuration
const NODE_SERVER_URL = 'http://localhost:3000'; // Default Node.js server URL
const WS_SERVER_URL = 'ws://localhost:3000';     // WebSocket server URL (same as HTTP port)

// WebSocket connection
let socket = null;
let isConnected = false;
let reconnectAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 10; // Increased max attempts
let reconnectTimer = null;
let connectionCheckInterval = null;

/**
 * Initialize the Node.js integration
 */
function initNodeIntegration() {
  console.log('Initializing Node.js integration');

  // Load configuration from storage
  chrome.storage.local.get(['nodeServerUrl', 'wsServerUrl'], (result) => {
    const nodeUrl = result.nodeServerUrl || NODE_SERVER_URL;
    const wsUrl = result.wsServerUrl || WS_SERVER_URL;

    console.log('Node server URL:', nodeUrl);
    console.log('WebSocket server URL:', wsUrl);

    // Initialize WebSocket connection
    connectWebSocket(wsUrl);

    // Register message listener for API requests from Node.js
    chrome.runtime.onMessageExternal.addListener(handleExternalMessage);
  });
}

/**
 * Connect to WebSocket server
 */
function connectWebSocket(url) {
  try {
    console.log('Attempting to connect to WebSocket server at:', url);

    // Clear any existing reconnect timers
    if (reconnectTimer) {
      clearTimeout(reconnectTimer);
      reconnectTimer = null;
    }

    // Close existing socket if it exists
    if (socket) {
      try {
        socket.close();
      } catch (e) {
        console.error('Error closing existing socket:', e);
      }
    }

    // Create a new WebSocket connection
    socket = new WebSocket(url);

    socket.onopen = () => {
      console.log('WebSocket connection established successfully');
      isConnected = true;
      reconnectAttempts = 0;

      // Send authentication message with extension ID
      const authMessage = {
        type: 'auth',
        extensionId: chrome.runtime.id,
        timestamp: Date.now()
      };
      console.log('Sending authentication message:', authMessage);

      socket.send(JSON.stringify(authMessage));

      // Set up a connection check interval
      if (connectionCheckInterval) {
        clearInterval(connectionCheckInterval);
      }

      connectionCheckInterval = setInterval(() => {
        if (socket && socket.readyState === WebSocket.OPEN) {
          // Send a heartbeat to keep the connection alive
          sendWebSocketMessage({
            type: 'heartbeat',
            timestamp: Date.now()
          });
        } else if (isConnected) {
          // Connection state mismatch - force reconnection
          console.log('Connection state mismatch detected. Reconnecting...');
          isConnected = false;
          attemptReconnect(url);
        }
      }, 30000); // Check every 30 seconds
    };

    socket.onmessage = (event) => {
      console.log('WebSocket message received:', event.data);
      handleWebSocketMessage(event.data);
    };

    socket.onclose = (event) => {
      console.log('WebSocket connection closed:', event.code, event.reason);
      isConnected = false;

      // Clear the connection check interval
      if (connectionCheckInterval) {
        clearInterval(connectionCheckInterval);
        connectionCheckInterval = null;
      }

      // Attempt to reconnect
      attemptReconnect(url);
    };

    socket.onerror = (error) => {
      console.error('WebSocket error:', error);
      // Don't set isConnected to false here, let onclose handle it
    };
  } catch (error) {
    console.error('Failed to connect to WebSocket server:', error);
    isConnected = false;

    // Try to reconnect after a delay
    attemptReconnect(url);
  }
}

/**
 * Attempt to reconnect to the WebSocket server
 */
function attemptReconnect(url) {
  if (reconnectTimer) {
    clearTimeout(reconnectTimer);
    reconnectTimer = null;
  }

  if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
    reconnectAttempts++;
    const delay = Math.min(5000 * reconnectAttempts, 30000); // Exponential backoff with max 30s
    console.log(`Attempting to reconnect (${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS}) in ${delay/1000}s...`);

    reconnectTimer = setTimeout(() => {
      reconnectTimer = null;
      connectWebSocket(url);
    }, delay);
  } else {
    console.error('Maximum reconnection attempts reached. Will try again in 2 minutes.');

    // Reset reconnect attempts after 2 minutes and try again
    reconnectTimer = setTimeout(() => {
      reconnectAttempts = 0;
      reconnectTimer = null;
      connectWebSocket(url);
    }, 120000); // 2 minutes
  }
}

/**
 * Handle WebSocket messages
 */
function handleWebSocketMessage(data) {
  try {
    const message = JSON.parse(data);

    switch (message.type) {
      case 'download_request':
        handleDownloadRequest(message);
        break;
      case 'ping':
        // Respond to ping with pong
        sendWebSocketMessage({
          type: 'pong',
          timestamp: Date.now()
        });
        break;
      case 'heartbeat_ack':
        // Server acknowledged our heartbeat
        console.log('Heartbeat acknowledged by server');
        break;
      case 'connection_status':
        // Update connection status
        console.log('Connection status update:', message.status);
        if (message.status === 'connected') {
          isConnected = true;
        }
        break;
      case 'close_tabs':
        handleCloseTabs();
        break;
      default:
        console.log('Unknown message type:', message.type);
    }
  } catch (error) {
    console.error('Error handling WebSocket message:', error);
  }
}

/**
 * Send message through WebSocket
 */
function sendWebSocketMessage(message) {
  if (isConnected && socket) {
    socket.send(JSON.stringify(message));
  } else {
    console.error('Cannot send message: WebSocket not connected');
  }
}

/**
 * Handle download request from Node.js
 */
function handleDownloadRequest(message) {
  const { url, options } = message;

  if (!url) {
    sendWebSocketMessage({
      type: 'error',
      error: 'No URL provided',
      requestId: message.requestId
    });
    return;
  }

  console.log('Received download request for URL:', url);

  // Forward the request to the background script
  chrome.runtime.sendMessage({
    action: 'nodeDownloadRequest',
    data: {
      url,
      options,
      requestId: message.requestId
    }
  });
}

/**
 * Handle close tabs request from Node.js
 */
function handleCloseTabs() {
  console.log('Received close tabs request');

  // Forward the request to the background script
  chrome.runtime.sendMessage({
    action: 'closeTabs'
  });

  // Send confirmation back to server
  sendWebSocketMessage({
    type: 'tabs_closed',
    timestamp: Date.now()
  });
}

/**
 * Handle external messages (from Node.js app via chrome.runtime.sendMessage)
 */
function handleExternalMessage(message, sender, sendResponse) {
  console.log('Received external message:', message);

  if (sender.url && !sender.url.startsWith(NODE_SERVER_URL)) {
    console.error('Message from unauthorized source:', sender.url);
    sendResponse({ error: 'Unauthorized' });
    return;
  }

  switch (message.action) {
    case 'download':
      handleDownloadRequest(message);
      sendResponse({ status: 'processing' });
      break;
    case 'getStatus':
      sendResponse({ status: 'online', extensionId: chrome.runtime.id });
      break;
    default:
      sendResponse({ error: 'Unknown action' });
  }
}

/**
 * Send download result to Node.js server
 */
function sendDownloadResult(data) {
  const { fileData, fileName, requestId } = data;

  // Send notification through WebSocket
  sendWebSocketMessage({
    type: 'download_complete',
    fileName,
    requestId
  });

  // Send the actual data through HTTP POST
  fetch(`${NODE_SERVER_URL}/api/receive-data`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      fileName,
      fileData,
      requestId,
      extensionId: chrome.runtime.id
    })
  })
  .then(response => response.json())
  .then(result => {
    console.log('Data sent to Node.js server:', result);
  })
  .catch(error => {
    console.error('Error sending data to Node.js server:', error);
  });
}

// Export functions
window.nodeIntegration = {
  init: initNodeIntegration,
  sendDownloadResult,
  sendWebSocketMessage,
  isConnected: () => isConnected,
  reconnect: (url) => {
    reconnectAttempts = 0;
    connectWebSocket(url || WS_SERVER_URL);
  },
  getConnectionStatus: () => ({
    isConnected,
    reconnectAttempts,
    maxReconnectAttempts: MAX_RECONNECT_ATTEMPTS
  })
};

// Initialize the Node.js integration when the script is loaded
document.addEventListener('DOMContentLoaded', () => {
  console.log('DOM content loaded, initializing Node.js integration');
  initNodeIntegration();
});
