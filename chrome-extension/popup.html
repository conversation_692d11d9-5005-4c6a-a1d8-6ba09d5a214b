<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Google Trends Data Downloader</title>
  <link rel="stylesheet" href="popup.css">
</head>
<body>
  <div class="container">
    <header>
      <h1>Google Trends Data Downloader</h1>
    </header>

    <div class="content">
      <div id="batch-download-container">
        <h2>Batch Download</h2>
        <div class="batch-controls">
          <div class="url-input-container">
            <label for="url-input">Add Google Trends URL:</label>
            <div class="url-input-row">
              <input type="text" id="url-input" placeholder="https://trends.google.com/trends/explore?..." />
              <button id="add-url-btn" class="icon-button" title="Add URL">+</button>
            </div>
            <div id="url-list-container">
              <h3>URL List</h3>
              <div id="url-list" class="url-list"></div>
              <div id="empty-list-message" class="empty-list-message">No URLs added yet</div>
            </div>
          </div>
          <div class="url-lists">
            <label for="saved-url-lists">Saved URL Lists:</label>
            <select id="saved-url-lists">
              <option value="">-- Select a saved list --</option>
            </select>
          </div>
          <div class="batch-buttons">
            <button id="create-list-btn">Create</button>
            <button id="delete-list-btn">Delete List</button>
          </div>
          <div class="batch-download-buttons">
            <button id="batch-download-all-btn">Batch Download All Data</button>
            <button id="batch-download-related-btn">Batch Download Related Queries</button>
            <div id="status-message" class="status-message">Ready to download Google Trends data</div>
          </div>
        </div>
        <div id="batch-progress" class="hidden">
          <h3>Batch Download Progress</h3>
          <div class="progress-container">
            <div class="progress-bar">
              <div id="progress-fill"></div>
            </div>
            <div id="progress-text">0/0 URLs processed</div>
          </div>
          <div id="current-url-container">
            <div>Current URL:</div>
            <div id="current-url" class="url-text"></div>
          </div>
          <button id="cancel-batch-btn">Cancel Batch Download</button>
        </div>
      </div>
    </div>

    <footer>
      <button id="batch-download-related-btn-footer">Batch Download Related Queries</button>
    </footer>
  </div>

  <script src="node-integration.js"></script>
  <script src="popup.js"></script>
</body>
</html>
