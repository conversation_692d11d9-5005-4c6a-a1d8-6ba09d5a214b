/* Google Trends Data Downloader - Content Styles */

/* Download button styles */
.gtrends-download-btn {
  background-color: #4285f4;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  margin-left: 10px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  white-space: nowrap;
}

.gtrends-download-btn:hover {
  background-color: #3367d6;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
  transform: translateY(-1px);
}

.gtrends-download-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
  box-shadow: none;
  transform: none;
}

/* Fixed position button styles */
.gtrends-fixed-btn {
  margin: 0;
  padding: 10px 20px;
  font-size: 14px;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
  z-index: 10000;
}

/* Button container styles */
.gtrends-button-container {
  display: flex;
  gap: 10px;
  margin-left: 10px;
}

/* Style for the "Download Related Queries" button */
#gtrends-download-related-queries-btn {
  background-color: #0f9d58; /* Google green */
}

#gtrends-download-related-queries-btn:hover {
  background-color: #0b8043; /* Darker green */
}

/* Add a download icon to the button */
.gtrends-download-btn::before {
  content: '';
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"><path fill="white" d="M5,20H19V18H5M19,9H15V3H9V9H5L12,16L19,9Z" /></svg>');
  background-repeat: no-repeat;
  background-position: center;
}

/* Toast notification styles */
.gtrends-toast {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: #323232;
  color: white;
  padding: 12px 24px;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  z-index: 10001;
  font-size: 14px;
  transition: transform 0.3s, opacity 0.3s;
  transform: translateY(100px);
  opacity: 0;
  max-width: 80%;
  word-wrap: break-word;
}

.gtrends-toast.show {
  transform: translateY(0);
  opacity: 1;
}

.gtrends-toast.success {
  background-color: #0f9d58;
}

.gtrends-toast.error {
  background-color: #db4437;
}

.gtrends-toast.info {
  background-color: #4285f4;
}

/* Progress indicator styles */
.gtrends-progress {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background-color: #f1f1f1;
  z-index: 10000;
}

.gtrends-progress-bar {
  height: 100%;
  background-color: #4285f4;
  width: 0;
  transition: width 0.3s;
}

/* Fixed container styles */
#gtrends-fixed-container {
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
  border-radius: 4px;
  overflow: hidden;
}
