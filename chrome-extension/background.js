/**
 * Google Trends Data Downloader - Background Script (Service Worker)
 *
 * This script handles background tasks like tracking downloads
 * and processing downloaded CSV files.
 */

import * as nodeIntegration from './node-integration-worker.js';

// State
let state = {
  downloads: [],
  lastDownloadTimestamp: null,
  currentTabId: null,

  // Batch download state
  batchDownload: {
    isActive: false,
    urls: [],
    currentIndex: 0,
    successfulUrls: 0,
    failedUrls: 0,
    onlyRelatedQueries: false,
    currentTab: null,
    timeoutId: null,
    isNodeRequest: false,
    requestId: null
  }
};

// Keep alive mechanism
let keepAliveInterval = null;

/**
 * Initialize the background script
 */
function initialize() {
  console.log('Google Trends Data Downloader: Background script initialized (Service Worker)');

  // Initialize Node.js integration
  nodeIntegration.initNodeIntegration();

  // Listen for messages from content script
  chrome.runtime.onMessage.addListener(handleMessage);

  // Listen for download events
  chrome.downloads.onCreated.addListener(handleDownloadCreated);
  chrome.downloads.onChanged.addListener(handleDownloadChanged);

  // Listen for tab updates
  chrome.tabs.onActivated.addListener(handleTabActivated);

  // Set up a keep-alive ping
  setupKeepAlive();

  // Set up a periodic connection check
  setInterval(checkNodeJsConnection, 60000); // Check every minute
}

/**
 * Set up a keep-alive mechanism to prevent the service worker from being terminated
 */
function setupKeepAlive() {
  if (keepAliveInterval) {
    clearInterval(keepAliveInterval);
  }

  keepAliveInterval = setInterval(() => {
    console.log('Service worker keep-alive ping at', new Date().toISOString());

    // Send a heartbeat to the Node.js server
    nodeIntegration.sendWebSocketMessage({
      type: 'heartbeat',
      timestamp: Date.now()
    });

    // Also check connection status
    checkNodeJsConnection();
  }, 30000); // Every 30 seconds (increased from 25s to match server expectations)
}

/**
 * Check the Node.js connection status and attempt to reconnect if needed
 */
function checkNodeJsConnection() {
  const status = nodeIntegration.getConnectionStatus();
  console.log('Node.js connection status:', status);

  // Log more detailed connection information
  if (status.readyStateName) {
    console.log(`WebSocket state: ${status.readyStateName}, last heartbeat: ${status.lastHeartbeatAge}s ago`);
  }

  if (!status.isConnected) {
    console.log('Node.js connection is down, attempting to reconnect');
    nodeIntegration.reconnect();

    // Schedule another check in 10 seconds to verify reconnection was successful
    setTimeout(() => {
      const newStatus = nodeIntegration.getConnectionStatus();
      console.log('Follow-up connection check status:', newStatus);

      if (!newStatus.isConnected && !newStatus.isConnecting) {
        console.log('Still not connected after reconnection attempt, trying again');
        nodeIntegration.reconnect();
      }
    }, 10000);
  }
}

/**
 * Handle messages from content script and popup
 */
function handleMessage(message, sender, sendResponse) {
  console.log('Google Trends Data Downloader: Received message', message);

  if (message.action === 'downloadsComplete') {
    console.log('Google Trends Data Downloader: Received downloadsComplete message', message.data);

    // Update the popup if it's open
    chrome.runtime.sendMessage({
      action: 'updateDownloadStatus',
      data: message.data
    });

    // Store the download information
    chrome.storage.local.set({
      lastDownload: {
        timestamp: new Date().toISOString(),
        data: message.data
      }
    });

    // If this is part of a batch download, move to the next URL
    if (state.batchDownload.isActive && state.batchDownload.currentTab) {
      // Mark this URL as successful
      state.batchDownload.successfulUrls++;

      // Close the current tab immediately to ensure we don't leave tabs open
      console.log('Google Trends Data Downloader: Closing tab after download completion', state.batchDownload.currentTab.id);

      try {
        chrome.tabs.remove(state.batchDownload.currentTab.id, () => {
          if (chrome.runtime.lastError) {
            console.error('Error closing tab:', chrome.runtime.lastError);
          } else {
            console.log('Successfully closed tab:', state.batchDownload.currentTab.id);
          }

          // Clear the current tab reference
          state.batchDownload.currentTab = null;

          // Process the next URL in the batch
          processNextBatchUrl();
        });
      } catch (error) {
        console.error('Error closing tab:', error);
        state.batchDownload.currentTab = null;
        processNextBatchUrl();
      }
    }

    // Send a response to confirm receipt
    if (sendResponse) {
      sendResponse({
        success: true,
        message: 'Download completion acknowledged, tab will be closed',
        timestamp: Date.now()
      });
    }

    // Return true to indicate we'll send a response asynchronously
    return true;
  }

  // Handle close tabs request from Node.js app
  if (message.action === 'closeTabs') {
    console.log('Google Trends Data Downloader: Received closeTabs request');

    // FORCE CLOSE ALL GOOGLE TRENDS TABS IMMEDIATELY
    forceCloseAllGoogleTrendsTabs(sendResponse);

    // Return true to indicate we'll send a response asynchronously
    return true;
  }

  // Handle close ALL tabs request (super aggressive)
  if (message.action === 'closeAllTabs') {
    console.log('Google Trends Data Downloader: Received URGENT closeAllTabs request');

    // Close ALL tabs except the extension pages
    chrome.tabs.query({}, (tabs) => {
      console.log(`Found ${tabs.length} total tabs to close`);

      // Close each tab immediately
      tabs.forEach(tab => {
        // Skip extension pages
        if (tab.url && tab.url.startsWith('chrome-extension://')) {
          console.log(`Skipping extension tab: ${tab.url}`);
          return;
        }

        try {
          chrome.tabs.remove(tab.id, () => {
            if (chrome.runtime.lastError) {
              console.error(`Error closing tab ${tab.id}:`, chrome.runtime.lastError);
            } else {
              console.log(`Successfully closed tab ${tab.id}`);
            }
          });
        } catch (error) {
          console.error(`Error closing tab ${tab.id}:`, error);
        }
      });

      // Clear the current tab reference
      if (state.batchDownload.currentTab) {
        state.batchDownload.currentTab = null;
      }

      // Send a response if callback is provided
      if (sendResponse) {
        sendResponse({
          success: true,
          message: `Force closed ${tabs.length} tabs`,
          timestamp: Date.now()
        });
      }

      // Process the next URL in the batch if active
      if (state.batchDownload.isActive) {
        processNextBatchUrl();
      }
    });

    // Return true to indicate we'll send a response asynchronously
    return true;
  }

  // Handle batch download requests from popup
  if (message.action === 'startBatchDownload') {
    startBatchDownload(message.data.urls, message.data.onlyRelatedQueries);
  }

  if (message.action === 'cancelBatchDownload') {
    cancelBatchDownload();
  }

  if (message.action === 'batchDownloadError') {
    console.log('Google Trends Data Downloader: Received batch download error', message.data);

    // Mark this URL as failed
    state.batchDownload.failedUrls++;

    // Close the current tab and move to the next URL
    if (state.batchDownload.currentTab) {
      chrome.tabs.remove(state.batchDownload.currentTab.id);
      state.batchDownload.currentTab = null;

      // Process the next URL
      processNextBatchUrl();
    }
  }

  // Handle Node.js integration requests
  if (message.action === 'nodeDownloadRequest') {
    console.log('Google Trends Data Downloader: Received Node.js download request', message.data);

    try {
      // Start a download for the requested URL
      const { url, options, requestId } = message.data;

      console.log(`Processing download request for URL: ${url}`);
      console.log(`Request ID: ${requestId}, Options:`, options);

      // Add to batch download queue with a special flag
      startBatchDownload(
        [url],
        options?.onlyRelatedQueries || false,
        { isNodeRequest: true, requestId }
      );

      // Send a response to confirm receipt of the request
      if (sendResponse) {
        sendResponse({
          success: true,
          message: 'Download request received and processing started',
          requestId
        });
      }

      // Also notify through the Node.js integration
      if (nodeIntegration && typeof nodeIntegration.sendWebSocketMessage === 'function') {
        nodeIntegration.sendWebSocketMessage({
          type: 'download_request_processing',
          requestId,
          success: true,
          message: 'Download request received and processing started',
          timestamp: Date.now()
        });
      }

      return true; // Keep the message channel open for the async response
    } catch (error) {
      console.error('Error processing Node.js download request:', error);

      // Send error response
      if (sendResponse) {
        sendResponse({
          success: false,
          message: 'Error processing download request',
          error: error.message,
          requestId: message.data?.requestId
        });
      }

      return true;
    }
  }

  // Handle download complete message from node-integration-worker.js
  if (message.action === 'downloadComplete') {
    console.log('Received download complete message:', message);

    // If this is a Node.js request, forward the message to the Node.js server
    if (message.requestId && nodeIntegration && typeof nodeIntegration.sendWebSocketMessage === 'function') {
      // Send download complete message
      nodeIntegration.sendWebSocketMessage({
        type: 'download_complete',
        requestId: message.requestId,
        fileName: message.fileName || 'unknown',
        stats: message.stats || {},
        tabId: message.tabId, // Include the tab ID for later closing
        timestamp: Date.now()
      });

      // Immediately close the tab after sending the completion message
      if (message.tabId) {
        console.log('Closing tab after download completion:', message.tabId);
        try {
          chrome.tabs.remove(message.tabId, () => {
            console.log('Tab closed after download completion:', message.tabId);

            // Send tabs closed confirmation
            nodeIntegration.sendWebSocketMessage({
              type: 'tabs_closed',
              count: 1,
              message: 'Tab closed after download completion',
              requestId: message.requestId,
              timestamp: Date.now()
            });
          });
        } catch (error) {
          console.error('Error closing tab after download completion:', error);
        }
      }

      // Also close any other Google Trends tabs
      chrome.tabs.query({url: "https://trends.google.com/*"}, (tabs) => {
        console.log(`Found ${tabs.length} Google Trends tabs to close after download completion`);

        if (tabs.length > 0) {
          tabs.forEach(tab => {
            try {
              chrome.tabs.remove(tab.id, () => {
                console.log(`Closed Google Trends tab ${tab.id} after download completion`);
              });
            } catch (error) {
              console.error(`Error closing Google Trends tab ${tab.id}:`, error);
            }
          });

          // Send tabs closed confirmation
          nodeIntegration.sendWebSocketMessage({
            type: 'tabs_closed',
            count: tabs.length,
            message: 'All Google Trends tabs closed after download completion',
            requestId: message.requestId,
            timestamp: Date.now()
          });
        }
      });
    }

    return true;
  }

  // Handle download failed message from node-integration-worker.js
  if (message.action === 'downloadFailed') {
    console.log('Received download failed message:', message);

    // If this is a Node.js request, forward the message to the Node.js server
    if (message.requestId && nodeIntegration && typeof nodeIntegration.sendWebSocketMessage === 'function') {
      nodeIntegration.sendWebSocketMessage({
        type: 'download_failed',
        requestId: message.requestId,
        error: message.error || 'Unknown error',
        timestamp: Date.now()
      });
    }

    return true;
  }

  // Check connection status
  if (message.action === 'checkConnectionStatus') {
    const status = nodeIntegration.getConnectionStatus();
    sendResponse({
      isConnected: status.isConnected,
      details: status
    });
    return true; // Keep the message channel open for the async response
  }

  // Get the current tab ID
  if (message.action === 'getTabId') {
    if (sender && sender.tab && sender.tab.id) {
      sendResponse({ tabId: sender.tab.id });
    } else {
      sendResponse({ tabId: null });
    }
    return true;
  }

  // Handle close tabs request
  if (message.action === 'closeTabs') {
    console.log('Received close tabs request from Node.js');

    // Get all tabs
    chrome.tabs.query({}, (tabs) => {
      // Find Google Trends tabs
      const trendsTabs = tabs.filter(tab =>
        tab.url && tab.url.includes('trends.google.com')
      );

      console.log(`Found ${trendsTabs.length} Google Trends tabs to close`);

      // Close each tab
      trendsTabs.forEach(tab => {
        chrome.tabs.remove(tab.id, () => {
          if (chrome.runtime.lastError) {
            console.error(`Error closing tab ${tab.id}:`, chrome.runtime.lastError);
          } else {
            console.log(`Closed tab ${tab.id}`);
          }
        });
      });
    });

    return true;
  }
}

/**
 * Handle download created event
 */
function handleDownloadCreated(downloadItem) {
  // Check if this is a Google Trends CSV download
  if (downloadItem.url.includes('trends.google.com') &&
      downloadItem.filename.endsWith('.csv')) {

    console.log('Google Trends Data Downloader: New download detected', downloadItem);

    // Add to our downloads list
    state.downloads.push({
      id: downloadItem.id,
      filename: downloadItem.filename,
      url: downloadItem.url,
      startTime: new Date().toISOString(),
      status: 'in_progress'
    });

    // Update the last download timestamp
    state.lastDownloadTimestamp = new Date().toISOString();

    // Update the popup if it's open
    chrome.runtime.sendMessage({
      action: 'downloadStarted',
      data: {
        id: downloadItem.id,
        filename: downloadItem.filename
      }
    });
  }
}

/**
 * Handle download changed event
 */
function handleDownloadChanged(downloadDelta) {
  // Find this download in our list
  const downloadIndex = state.downloads.findIndex(d => d.id === downloadDelta.id);

  if (downloadIndex === -1) {
    return;
  }

  const download = state.downloads[downloadIndex];

  // Check if the download completed
  if (downloadDelta.state && downloadDelta.state.current === 'complete') {
    console.log('Google Trends Data Downloader: Download completed', download.filename);

    // Update the download status
    download.status = 'complete';
    download.completeTime = new Date().toISOString();

    // Process the downloaded file
    processDownloadedFile(download);

    // Update the popup if it's open
    chrome.runtime.sendMessage({
      action: 'downloadCompleted',
      data: {
        id: download.id,
        filename: download.filename
      }
    });

    // IMMEDIATELY force close all Google Trends tabs after download
    console.log('Google Trends Data Downloader: IMMEDIATELY force closing all tabs after download completion');

    // First close the current batch tab if it exists
    if (state.batchDownload.currentTab) {
      try {
        chrome.tabs.remove(state.batchDownload.currentTab.id, () => {
          console.log('Closed current batch download tab:', state.batchDownload.currentTab.id);
          state.batchDownload.currentTab = null;
        });
      } catch (error) {
        console.error('Error closing current batch tab:', error);
      }
    }

    // Then close all Google Trends tabs
    chrome.tabs.query({url: "https://trends.google.com/*"}, (tabs) => {
      console.log(`Found ${tabs.length} Google Trends tabs to close after download completion`);

      if (tabs.length > 0) {
        tabs.forEach(tab => {
          try {
            chrome.tabs.remove(tab.id, () => {
              console.log(`Closed Google Trends tab ${tab.id} after download completion`);
            });
          } catch (error) {
            console.error(`Error closing Google Trends tab ${tab.id}:`, error);
          }
        });
      }
    });

    // Also try the regular force close function with a slight delay as backup
    setTimeout(() => {
      forceCloseAllGoogleTrendsTabs();
    }, 500);
  }

  // Check if the download failed
  if (downloadDelta.error) {
    console.log('Google Trends Data Downloader: Download failed', download.filename, downloadDelta.error);

    // Update the download status
    download.status = 'error';
    download.error = downloadDelta.error.current;

    // Update the popup if it's open
    chrome.runtime.sendMessage({
      action: 'downloadFailed',
      data: {
        id: download.id,
        filename: download.filename,
        error: downloadDelta.error.current
      }
    });
  }
}

/**
 * Process a downloaded CSV file
 */
function processDownloadedFile(download) {
  // We can't directly access the file system from the extension
  // Instead, we'll store information about the download

  console.log('Processing downloaded file:', download.filename);

  // Mark this URL as successful
  state.batchDownload.successfulUrls++;

  // Get the current list of processed files
  chrome.storage.local.get('processedFiles', (result) => {
    const processedFiles = result.processedFiles || [];

    // Add this file to the list
    processedFiles.push({
      filename: download.filename,
      downloadTime: download.completeTime,
      url: download.url
    });

    // Store the updated list
    chrome.storage.local.set({ processedFiles });

    // If this is a Node.js request, send the data
    if (state.batchDownload.isNodeRequest && state.batchDownload.requestId) {
      console.log('Sending download data to Node.js server for file:', download.filename);
      console.log('Request ID:', state.batchDownload.requestId);

      try {
        // We need to fetch the file content
        // For CSV files, we can use the Fetch API to get the content
        fetch(download.url)
          .then(response => {
            if (!response.ok) {
              throw new Error(`Failed to fetch file: ${response.status} ${response.statusText}`);
            }
            return response.text();
          })
          .then(fileData => {
            console.log(`Successfully fetched file content (${fileData.length} bytes)`);

            // Send the data to the Node.js server
            if (nodeIntegration && typeof nodeIntegration.sendDownloadResult === 'function') {
              nodeIntegration.sendDownloadResult({
                fileData,
                fileName: download.filename,
                requestId: state.batchDownload.requestId
              });

              console.log('Download result sent to Node.js server');

              // IMMEDIATELY close all tabs
              console.log('Google Trends Data Downloader: IMMEDIATELY closing all tabs after sending download result');

              // First close the current batch tab if it exists
              if (state.batchDownload.currentTab) {
                try {
                  chrome.tabs.remove(state.batchDownload.currentTab.id, () => {
                    console.log('Closed current batch download tab after sending result:', state.batchDownload.currentTab.id);
                    state.batchDownload.currentTab = null;
                  });
                } catch (error) {
                  console.error('Error closing current batch tab after sending result:', error);
                }
              }

              // Then close all Google Trends tabs
              chrome.tabs.query({url: "https://trends.google.com/*"}, (tabs) => {
                console.log(`Found ${tabs.length} Google Trends tabs to close after sending result`);

                if (tabs.length > 0) {
                  tabs.forEach(tab => {
                    try {
                      chrome.tabs.remove(tab.id, () => {
                        console.log(`Closed Google Trends tab ${tab.id} after sending result`);
                      });
                    } catch (error) {
                      console.error(`Error closing Google Trends tab ${tab.id} after sending result:`, error);
                    }
                  });
                }
              });

              // Also try the regular force close function with a slight delay as backup
              setTimeout(() => {
                forceCloseAllGoogleTrendsTabs();
              }, 500);
            } else {
              console.error('nodeIntegration.sendDownloadResult is not available');

              // Try to send a message through WebSocket
              if (nodeIntegration && typeof nodeIntegration.sendWebSocketMessage === 'function') {
                nodeIntegration.sendWebSocketMessage({
                  type: 'download_complete',
                  fileName: download.filename,
                  requestId: state.batchDownload.requestId,
                  timestamp: Date.now()
                });

                console.log('Download completion notification sent via WebSocket');

                // IMMEDIATELY close all tabs
                console.log('Google Trends Data Downloader: IMMEDIATELY closing all tabs after sending WebSocket message');

                // First close the current batch tab if it exists
                if (state.batchDownload.currentTab) {
                  try {
                    chrome.tabs.remove(state.batchDownload.currentTab.id, () => {
                      console.log('Closed current batch download tab after WebSocket message:', state.batchDownload.currentTab.id);
                      state.batchDownload.currentTab = null;
                    });
                  } catch (error) {
                    console.error('Error closing current batch tab after WebSocket message:', error);
                  }
                }

                // Then close all Google Trends tabs
                chrome.tabs.query({url: "https://trends.google.com/*"}, (tabs) => {
                  console.log(`Found ${tabs.length} Google Trends tabs to close after WebSocket message`);

                  if (tabs.length > 0) {
                    tabs.forEach(tab => {
                      try {
                        chrome.tabs.remove(tab.id, () => {
                          console.log(`Closed Google Trends tab ${tab.id} after WebSocket message`);
                        });
                      } catch (error) {
                        console.error(`Error closing Google Trends tab ${tab.id} after WebSocket message:`, error);
                      }
                    });
                  }
                });

                // Also try the regular force close function with a slight delay as backup
                setTimeout(() => {
                  forceCloseAllGoogleTrendsTabs();
                }, 500);
              }
            }
          })
          .catch(error => {
            console.error('Error fetching file content:', error);

            // Notify the Node.js server about the error
            if (nodeIntegration && typeof nodeIntegration.sendWebSocketMessage === 'function') {
              nodeIntegration.sendWebSocketMessage({
                type: 'download_failed',
                requestId: state.batchDownload.requestId,
                error: `Error fetching file content: ${error.message}`,
                timestamp: Date.now()
              });
            }
          });
      } catch (error) {
        console.error('Error in fetch operation:', error);

        // Notify the Node.js server about the error
        if (nodeIntegration && typeof nodeIntegration.sendWebSocketMessage === 'function') {
          nodeIntegration.sendWebSocketMessage({
            type: 'download_failed',
            requestId: state.batchDownload.requestId,
            error: `Error in fetch operation: ${error.message}`,
            timestamp: Date.now()
          });
        }
      }
    } else {
      // IMMEDIATELY close all tabs even if not a Node.js request
      console.log('Google Trends Data Downloader: IMMEDIATELY closing all tabs after processing download');

      // First close the current batch tab if it exists
      if (state.batchDownload.currentTab) {
        try {
          chrome.tabs.remove(state.batchDownload.currentTab.id, () => {
            console.log('Closed current batch download tab after processing:', state.batchDownload.currentTab.id);
            state.batchDownload.currentTab = null;
          });
        } catch (error) {
          console.error('Error closing current batch tab after processing:', error);
        }
      }

      // Then close all Google Trends tabs
      chrome.tabs.query({url: "https://trends.google.com/*"}, (tabs) => {
        console.log(`Found ${tabs.length} Google Trends tabs to close after processing`);

        if (tabs.length > 0) {
          tabs.forEach(tab => {
            try {
              chrome.tabs.remove(tab.id, () => {
                console.log(`Closed Google Trends tab ${tab.id} after processing`);
              });
            } catch (error) {
              console.error(`Error closing Google Trends tab ${tab.id} after processing:`, error);
            }
          });
        }
      });

      // Also try the regular force close function with a slight delay as backup
      setTimeout(() => {
        forceCloseAllGoogleTrendsTabs();
      }, 500);
    }
  });
}

/**
 * Force close all Google Trends tabs - AGGRESSIVE VERSION
 */
function forceCloseAllGoogleTrendsTabs(sendResponse) {
  console.log('Google Trends Data Downloader: AGGRESSIVELY Force closing all Google Trends tabs');

  // First, try to close all tabs with Google Trends URLs
  chrome.tabs.query({url: "https://trends.google.com/*"}, (tabs) => {
    console.log(`Found ${tabs.length} Google Trends tabs to close`);

    // Close each tab immediately
    if (tabs.length > 0) {
      tabs.forEach(tab => {
        try {
          chrome.tabs.remove(tab.id, () => {
            if (chrome.runtime.lastError) {
              console.error(`Error closing tab ${tab.id}:`, chrome.runtime.lastError);
            } else {
              console.log(`Successfully closed tab ${tab.id}`);
            }
          });
        } catch (error) {
          console.error(`Error closing tab ${tab.id}:`, error);
        }
      });
    }

    // Also try to close the current batch tab if it exists
    if (state.batchDownload.currentTab) {
      try {
        chrome.tabs.remove(state.batchDownload.currentTab.id, () => {
          console.log('Closed current batch download tab');
          state.batchDownload.currentTab = null;
        });
      } catch (error) {
        console.error('Error closing current batch tab:', error);
      }
    }

    // Now, as a fallback, try to close ANY recently created tabs (last 5 minutes)
    // This is more aggressive but will ensure tabs get closed
    const fiveMinutesAgo = Date.now() - (5 * 60 * 1000);
    chrome.tabs.query({}, (allTabs) => {
      console.log(`Checking ${allTabs.length} total tabs for any that might be related to Google Trends`);

      // Filter to tabs created in the last 5 minutes
      const recentTabs = allTabs.filter(tab => {
        // If we can't determine when it was created, include it to be safe
        if (!tab.id) return true;

        // Include any tab that might be related to our downloads
        return true;
      });

      console.log(`Found ${recentTabs.length} recent tabs that might be related to Google Trends`);

      // Close these tabs too
      recentTabs.forEach(tab => {
        try {
          chrome.tabs.remove(tab.id, () => {
            console.log(`Closed potentially related tab ${tab.id}`);
          });
        } catch (error) {
          console.error(`Error closing potentially related tab ${tab.id}:`, error);
        }
      });

      // Send a response if callback is provided
      if (sendResponse) {
        sendResponse({
          success: true,
          message: `Force closed ${tabs.length} Google Trends tabs and ${recentTabs.length} potentially related tabs`,
          timestamp: Date.now()
        });
      }

      // Clear the current tab reference
      state.batchDownload.currentTab = null;

      // Process the next URL in the batch if active
      if (state.batchDownload.isActive) {
        processNextBatchUrl();
      }
    });
  });
}

/**
 * Close the current tab after a download is complete
 */
function closeTabAfterDownload() {
  // If this is part of a batch download, close the tab and move to the next URL
  if (state.batchDownload.isActive && state.batchDownload.currentTab) {
    console.log('Google Trends Data Downloader: Closing tab after download completion', state.batchDownload.currentTab.id);

    // Force close all Google Trends tabs to ensure nothing stays open
    forceCloseAllGoogleTrendsTabs();
  }
}

/**
 * Handle tab activated event
 */
function handleTabActivated(activeInfo) {
  // Store the current tab ID
  state.currentTabId = activeInfo.tabId;
}

/**
 * Start a batch download process
 * @param {Array} urls - Array of URLs to download
 * @param {boolean} onlyRelatedQueries - Whether to only download related queries
 * @param {Object} options - Additional options
 */
function startBatchDownload(urls, onlyRelatedQueries, options = {}) {
  // Ensure onlyRelatedQueries is a proper boolean
  const relatedQueriesFlag = onlyRelatedQueries === true;

  console.log('Google Trends Data Downloader: Starting batch download', {
    urls,
    onlyRelatedQueries: relatedQueriesFlag,
    originalValue: onlyRelatedQueries,
    options
  });

  // Reset batch download state
  state.batchDownload = {
    isActive: true,
    urls: urls,
    currentIndex: 0,
    successfulUrls: 0,
    failedUrls: 0,
    onlyRelatedQueries: relatedQueriesFlag,
    currentTab: null,
    timeoutId: null,
    // Node.js integration options
    isNodeRequest: options.isNodeRequest || false,
    requestId: options.requestId || null
  };

  // Start processing the first URL
  processNextBatchUrl();
}

// Make startBatchDownload globally accessible for direct calls from other contexts
if (typeof self !== 'undefined') {
  self.startBatchDownload = startBatchDownload;
}

// Also expose it on the window object if available (for Manifest V2)
if (typeof window !== 'undefined') {
  window.startBatchDownload = startBatchDownload;
}

/**
 * Process the next URL in the batch
 */
function processNextBatchUrl() {
  // Clear any existing timeout
  if (state.batchDownload.timeoutId) {
    clearTimeout(state.batchDownload.timeoutId);
    state.batchDownload.timeoutId = null;
  }

  // Check if we've processed all URLs
  if (state.batchDownload.currentIndex >= state.batchDownload.urls.length) {
    finishBatchDownload();
    return;
  }

  const currentUrl = state.batchDownload.urls[state.batchDownload.currentIndex];

  // Update the popup with progress
  chrome.runtime.sendMessage({
    action: 'batchDownloadProgress',
    data: {
      currentIndex: state.batchDownload.currentIndex,
      totalUrls: state.batchDownload.urls.length,
      currentUrl: currentUrl
    }
  });

  // Open the URL in a new tab
  chrome.tabs.create({ url: currentUrl, active: true }, (tab) => {
    state.batchDownload.currentTab = tab;

    // Focus the window containing the tab
    chrome.windows.update(tab.windowId, { focused: true });

    // Set a timeout to inject the download script after the page loads
    state.batchDownload.timeoutId = setTimeout(() => {
      injectDownloadScript(tab.id);
    }, 7000); // Wait 7 seconds for the page to fully load
  });

  // Increment the current index
  state.batchDownload.currentIndex++;
}

/**
 * Inject the download script into the tab
 */
function injectDownloadScript(tabId) {
  console.log('Google Trends Data Downloader: Injecting download script into tab', tabId);

  // First, check if the content script is loaded
  chrome.scripting.executeScript({
    target: { tabId: tabId },
    function: () => {
      return {
        contentScriptLoaded: typeof window.state !== 'undefined',
        buttonsExist: !!(
          document.getElementById('gtrends-download-all-btn') ||
          document.getElementById('gtrends-download-related-queries-btn')
        ),
        url: window.location.href
      };
    }
  }).then(results => {
    const result = results[0].result;
    console.log('Content script check result:', result);

    if (!result.contentScriptLoaded || !result.buttonsExist) {
      console.log('Content script not loaded or buttons not found, injecting content script manually');

      // Inject the content script manually
      chrome.scripting.executeScript({
        target: { tabId: tabId },
        files: ['content.js']
      }).then(() => {
        console.log('Content script injected, waiting for buttons to appear');

        // Wait a bit for the buttons to be added
        setTimeout(() => {
          // Now try to click the download button
          triggerDownload(tabId);
        }, 3000); // Wait 3 seconds for buttons to appear
      }).catch(error => {
        console.error('Error injecting content script:', error);
        handleDownloadError(error.message);
      });
    } else {
      // Content script is loaded and buttons exist, proceed with download
      triggerDownload(tabId);
    }
  }).catch(error => {
    console.error('Error checking content script:', error);
    handleDownloadError(error.message);
  });

  // Function to trigger the download
  function triggerDownload(tabId) {
    try {
      // Ensure onlyRelatedQueries is a proper boolean
      const relatedQueriesFlag = state.batchDownload.onlyRelatedQueries === true;

      // Log the onlyRelatedQueries value before passing it to the content script
      console.log('Google Trends Data Downloader: Triggering download with onlyRelatedQueries =', relatedQueriesFlag,
                  '(original value:', state.batchDownload.onlyRelatedQueries, ')');

      chrome.scripting.executeScript({
        target: { tabId: tabId },
        args: [relatedQueriesFlag],
        function: (onlyRelatedQueries) => {
          try {
            // This function runs in the context of the page
            console.log('Google Trends Data Downloader: Download script injected, onlyRelatedQueries =', onlyRelatedQueries);

            // Set the download mode
            if (typeof window.state !== 'undefined') {
              // Explicitly set the onlyRelatedQueries flag as a boolean
              window.state.onlyRelatedQueries = onlyRelatedQueries === true;
              console.log('Google Trends Data Downloader: Set window.state.onlyRelatedQueries =', window.state.onlyRelatedQueries);

              // Find and click the appropriate download button
              const downloadAllBtn = document.getElementById('gtrends-download-all-btn');
              const downloadRelatedQueriesBtn = document.getElementById('gtrends-download-related-queries-btn');

              if (onlyRelatedQueries && downloadRelatedQueriesBtn) {
                console.log('Google Trends Data Downloader: Clicking "Download Related Queries" button');
                downloadRelatedQueriesBtn.click();
                return { success: true, button: 'related-queries' };
              } else if (downloadAllBtn) {
                console.log('Google Trends Data Downloader: Clicking "Download All Data" button');
                downloadAllBtn.click();
                return { success: true, button: 'all-data' };
              } else {
                console.log('Google Trends Data Downloader: Download buttons not found, trying direct download');

                // Try to find and click export buttons directly
                const exportButtons = document.querySelectorAll('button.export');
                if (exportButtons.length > 0) {
                  console.log(`Found ${exportButtons.length} export buttons, clicking the first one`);
                  exportButtons[0].click();

                  // Wait a bit and try to click the CSV option
                  setTimeout(function() {
                    try {
                      const csvOptions = document.querySelectorAll('button.md-button[ng-click*="csv"], md-menu-item button[ng-click*="csv"]');
                      if (csvOptions.length > 0) {
                        console.log(`Found ${csvOptions.length} CSV options, clicking the first one`);
                        csvOptions[0].click();
                      }
                    } catch (innerError) {
                      console.error('Error clicking CSV option:', innerError);
                    }
                  }, 1000);

                  return { success: true, button: 'direct-export' };
                }

                return { success: false, error: 'Download buttons not found' };
              }
            } else {
              console.log('Google Trends Data Downloader: window.state not found, content script may not be loaded');
              return { success: false, error: 'Content script not loaded properly' };
            }
          } catch (error) {
            console.error('Error in content script execution:', error);
            return { success: false, error: `Error in content script: ${error.message}` };
          }
        },
        args: [relatedQueriesFlag]
      }).then(results => {
        if (!results || results.length === 0) {
          console.error('No results returned from executeScript');
          handleDownloadError('No results returned from executeScript');
          return;
        }

        const result = results[0].result;
        console.log('Download trigger result:', result);

        if (!result || !result.success) {
          handleDownloadError(result?.error || 'Unknown error triggering download');
        }
      }).catch(error => {
        console.error('Error executing download script:', error);
        handleDownloadError(error.message || 'Unknown error executing script');
      });
    } catch (outerError) {
      console.error('Error in triggerDownload function:', outerError);
      handleDownloadError(outerError.message || 'Unknown error in triggerDownload');
    }
  }

  // Function to handle download errors
  function handleDownloadError(errorMessage) {
    console.error('Google Trends Data Downloader: Download error:', errorMessage);

    // Mark this URL as failed
    state.batchDownload.failedUrls++;

    // If this is a Node.js request, send the error
    if (state.batchDownload.isNodeRequest && state.batchDownload.requestId) {
      try {
        if (nodeIntegration && typeof nodeIntegration.sendWebSocketMessage === 'function') {
          console.log('Sending error message to Node.js server:', {
            type: 'download_failed',
            requestId: state.batchDownload.requestId,
            error: errorMessage
          });

          nodeIntegration.sendWebSocketMessage({
            type: 'download_failed',
            requestId: state.batchDownload.requestId,
            error: errorMessage,
            timestamp: Date.now()
          });
        } else {
          console.error('nodeIntegration.sendWebSocketMessage is not available');
        }
      } catch (error) {
        console.error('Error sending failure message to Node.js server:', error);
      }
    }

    // Close the tab and move to the next URL
    if (state.batchDownload.currentTab) {
      chrome.tabs.remove(state.batchDownload.currentTab.id);
      state.batchDownload.currentTab = null;
    }

    // Process the next URL
    processNextBatchUrl();
  }

  // Set a timeout to move to the next URL if this one takes too long
  state.batchDownload.timeoutId = setTimeout(() => {
    console.log('Google Trends Data Downloader: Timeout waiting for downloads, moving to next URL');

    // Before giving up, check if any downloads have completed
    chrome.downloads.search({}, (downloads) => {
      // Look for recent Google Trends CSV downloads
      const recentTrendsDownloads = downloads.filter(download =>
        download.filename.includes('relatedQueries') &&
        download.filename.endsWith('.csv') &&
        download.state === 'complete' &&
        download.endTime &&
        (new Date().getTime() - new Date(download.endTime).getTime() < 120000) // Within the last 2 minutes
      );

      if (recentTrendsDownloads.length > 0) {
        console.log('Found recent Google Trends downloads:', recentTrendsDownloads);

        // Process the most recent download
        const mostRecentDownload = recentTrendsDownloads.sort((a, b) =>
          new Date(b.endTime).getTime() - new Date(a.endTime).getTime()
        )[0];

        // Create a download object to process
        const downloadObj = {
          id: mostRecentDownload.id,
          filename: mostRecentDownload.filename,
          url: mostRecentDownload.url || 'https://trends.google.com',
          completeTime: mostRecentDownload.endTime,
          status: 'complete'
        };

        // Process the downloaded file
        processDownloadedFile(downloadObj);

        // If this is a Node.js request, send a success message
        if (state.batchDownload.isNodeRequest && state.batchDownload.requestId) {
          if (nodeIntegration && typeof nodeIntegration.sendWebSocketMessage === 'function') {
            nodeIntegration.sendWebSocketMessage({
              type: 'download_complete',
              requestId: state.batchDownload.requestId,
              fileName: mostRecentDownload.filename,
              timestamp: Date.now()
            });
          }
        }

        return; // Don't proceed with the failure path
      }

      // If no recent downloads found, proceed with failure

      // Mark this URL as failed
      state.batchDownload.failedUrls++;

      // If this is a Node.js request, send the timeout error
      if (state.batchDownload.isNodeRequest && state.batchDownload.requestId) {
        if (nodeIntegration && typeof nodeIntegration.sendWebSocketMessage === 'function') {
          nodeIntegration.sendWebSocketMessage({
            type: 'download_failed',
            requestId: state.batchDownload.requestId,
            error: 'Download timeout - no response after 60 seconds',
            timestamp: Date.now()
          });
        }
      }

      // Close the tab and move to the next URL
      if (state.batchDownload.currentTab) {
        try {
          chrome.tabs.remove(state.batchDownload.currentTab.id, () => {
            if (chrome.runtime.lastError) {
              console.error('Error closing tab during timeout:', chrome.runtime.lastError);
            } else {
              console.log('Successfully closed tab during timeout:', state.batchDownload.currentTab.id);
            }

            // Clear the current tab reference
            state.batchDownload.currentTab = null;

            // Process the next URL
            processNextBatchUrl();
          });
        } catch (error) {
          console.error('Error closing tab during timeout:', error);
          state.batchDownload.currentTab = null;
          processNextBatchUrl();
        }
      } else {
        // Process the next URL
        processNextBatchUrl();
      }
    });
  }, 60000); // 60 second timeout (increased from 30s)
}

/**
 * Finish the batch download process
 */
function finishBatchDownload() {
  console.log('Google Trends Data Downloader: Batch download complete', {
    totalUrls: state.batchDownload.urls.length,
    successfulUrls: state.batchDownload.successfulUrls,
    failedUrls: state.batchDownload.failedUrls,
    isNodeRequest: state.batchDownload.isNodeRequest
  });

  // Send a message to the popup
  chrome.runtime.sendMessage({
    action: 'batchDownloadComplete',
    data: {
      totalUrls: state.batchDownload.urls.length,
      successfulUrls: state.batchDownload.successfulUrls,
      failedUrls: state.batchDownload.failedUrls
    }
  });

  // If this was a Node.js request, send a completion or failure message
  if (state.batchDownload.isNodeRequest && state.batchDownload.requestId) {
    if (state.batchDownload.successfulUrls > 0) {
      // Send a completion message
      console.log('Sending batch download completion notification to Node.js server');

      // Try both methods to ensure the message gets through

      // Method 1: Using the nodeIntegration module
      if (nodeIntegration && typeof nodeIntegration.sendWebSocketMessage === 'function') {
        nodeIntegration.sendWebSocketMessage({
          type: 'download_complete',
          requestId: state.batchDownload.requestId,
          stats: {
            total: state.batchDownload.urls.length,
            successful: state.batchDownload.successfulUrls,
            failed: state.batchDownload.failedUrls
          },
          timestamp: Date.now()
        });
      }

      // Method 2: Sending a message to the service worker
      chrome.runtime.sendMessage({
        action: 'downloadComplete',
        requestId: state.batchDownload.requestId,
        stats: {
          total: state.batchDownload.urls.length,
          successful: state.batchDownload.successfulUrls,
          failed: state.batchDownload.failedUrls
        }
      });
    } else {
      // Send a failure message
      console.log('Sending batch download failure notification to Node.js server');

      // Try both methods to ensure the message gets through

      // Method 1: Using the nodeIntegration module
      if (nodeIntegration && typeof nodeIntegration.sendWebSocketMessage === 'function') {
        nodeIntegration.sendWebSocketMessage({
          type: 'download_failed',
          requestId: state.batchDownload.requestId,
          error: 'No files were downloaded successfully',
          timestamp: Date.now()
        });
      }

      // Method 2: Sending a message to the service worker
      chrome.runtime.sendMessage({
        action: 'downloadFailed',
        requestId: state.batchDownload.requestId,
        error: 'No files were downloaded successfully'
      });
    }
  }

  // Reset the batch download state
  state.batchDownload.isActive = false;
}

/**
 * Cancel the current batch download
 */
function cancelBatchDownload() {
  console.log('Google Trends Data Downloader: Cancelling batch download');

  // Clear any existing timeout
  if (state.batchDownload.timeoutId) {
    clearTimeout(state.batchDownload.timeoutId);
    state.batchDownload.timeoutId = null;
  }

  // Close the current tab if it exists
  if (state.batchDownload.currentTab) {
    chrome.tabs.remove(state.batchDownload.currentTab.id);
    state.batchDownload.currentTab = null;
  }

  // Reset the batch download state
  state.batchDownload.isActive = false;
}

// Initialize the background script when the service worker starts
initialize();

// Listen for service worker activation
self.addEventListener('activate', (event) => {
  console.log('Service worker activated at', new Date().toISOString());

  // Claim control immediately
  event.waitUntil(clients.claim());

  // Make sure the Node.js integration is initialized
  nodeIntegration.initNodeIntegration();

  // Set up keep-alive
  setupKeepAlive();
});

// Keep the service worker alive
self.addEventListener('fetch', (event) => {
  // This empty fetch handler helps keep the service worker active
  // No need to actually do anything with the fetch event
});

// Handle messages from clients
self.addEventListener('message', (event) => {
  console.log('Service worker received message:', event.data);

  if (event.data.action === 'ping') {
    // Respond to ping
    event.source.postMessage({
      action: 'pong',
      timestamp: Date.now()
    });
  }

  if (event.data.action === 'checkConnection') {
    // Check Node.js connection
    const status = nodeIntegration.getConnectionStatus();
    event.source.postMessage({
      action: 'connectionStatus',
      status: status
    });
  }
});
