'use strict';
module.exports = {
  ["addJob-6"]: require('./addJob-6'),
  ["addLog-2"]: require('./addLog-2'),
  ["cleanJobsInSet-3"]: require('./cleanJobsInSet-3'),
  ["extendLock-2"]: require('./extendLock-2'),
  ["getCountsPerPriority-4"]: require('./getCountsPerPriority-4'),
  ["isFinished-2"]: require('./isFinished-2'),
  ["isJobInList-1"]: require('./isJobInList-1'),
  ["moveStalledJobsToWait-7"]: require('./moveStalledJobsToWait-7'),
  ["moveToActive-8"]: require('./moveToActive-8'),
  ["moveToDelayed-4"]: require('./moveToDelayed-4'),
  ["moveToFinished-9"]: require('./moveToFinished-9'),
  ["obliterate-2"]: require('./obliterate-2'),
  ["pause-5"]: require('./pause-5'),
  ["promote-5"]: require('./promote-5'),
  ["releaseLock-1"]: require('./releaseLock-1'),
  ["removeJob-11"]: require('./removeJob-11'),
  ["removeJobs-8"]: require('./removeJobs-8'),
  ["removeRepeatable-2"]: require('./removeRepeatable-2'),
  ["reprocessJob-6"]: require('./reprocessJob-6'),
  ["retryJob-7"]: require('./retryJob-7'),
  ["retryJobs-5"]: require('./retryJobs-5'),
  ["saveStacktrace-1"]: require('./saveStacktrace-1'),
  ["takeLock-1"]: require('./takeLock-1'),
  ["updateData-1"]: require('./updateData-1'),
  ["updateDelaySet-6"]: require('./updateDelaySet-6'),
  ["updateProgress-2"]: require('./updateProgress-2'),
}
