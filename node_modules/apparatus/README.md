
apparatus
=========

A collection of low-level machine learning algorithms for node.js.

This project is quite new and documentation will be on the way shortly.
In the meantime you can check out the spec folder for examples of how
to use the algorithms.

Note that within "apparatus" the interface to the algorithms in
primarily arrays of numbers and vectors. If you're  looking for feature
extraction from text or natural language check out the "natural"
[https://github.com/NaturalNode/natural](https://github.com/NaturalNode/natural) node package. "natural" uses
many of these algorithms but adds a layer of natural language/text
feature extraction.
