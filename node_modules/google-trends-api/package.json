{"name": "google-trends-api", "version": "4.9.2", "description": "an API layer on top of google trends", "main": "lib/google-trends-api.min.js", "scripts": {"build": "webpack --mode=build", "dev": "webpack --progress --colors --watch --mode=dev", "start": "npm run dev", "test": "mocha --compilers js:babel-core/register --colors ./test/*.spec.js", "test:watch": "mocha --compilers js:babel-core/register --colors -w ./test/*.spec.js", "cover": "nyc --reporter=lcov mocha --compilers js:babel-core/register --colors ./test/*.spec.js", "coveralls": "npm run cover && nyc report --reporter=text-lcov | coveralls", "coverage": "nyc mocha --compilers js:babel-core/register --colors ./test/*.spec.js", "preversion": "npm run build", "lint:eslint": "eslint src, test", "lint:staged": "lint-staged"}, "lint-staged": {"*.js": "lint:eslint"}, "pre-commit": "lint:staged", "repository": {"type": "git", "url": "git+https://github.com/pat310/google-trends-api.git"}, "keywords": ["google", "trends", "API", "keyword", "search", "google-trends"], "author": "<PERSON> <<EMAIL>> (http://trasb.org)", "license": "MIT", "bugs": {"url": "https://github.com/pat310/google-trends-api/issues"}, "files": ["src/*"], "homepage": "https://github.com/pat310/google-trends-api#readme", "dependencies": {}, "devDependencies": {"babel": "6.3.13", "babel-core": "6.1.18", "babel-eslint": "5.0.0", "babel-loader": "6.1.0", "babel-plugin-add-module-exports": "0.1.2", "babel-plugin-transform-es2015-destructuring": "^6.23.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-preset-es2015": "6.3.13", "chai": "3.4.1", "coveralls": "^2.11.15", "eslint": "1.7.2", "eslint-loader": "1.1.0", "istanbul": "^0.4.5", "json-loader": "^0.5.4", "lint-staged": "^4.0.3", "mocha": "2.3.4", "mocha-lcov-reporter": "^1.2.0", "nyc": "^10.1.2", "pre-commit": "^1.2.2", "webpack": "1.12.9", "yargs": "3.32.0"}}