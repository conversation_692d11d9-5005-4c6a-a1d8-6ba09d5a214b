!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("https"),require("querystring")):"function"==typeof define&&define.amd?define("google-trends-api",["https","querystring"],t):"object"==typeof exports?exports["google-trends-api"]=t(require("https"),require("querystring")):e["google-trends-api"]=t(e.https,e.querystring)}(this,function(e,t){return function(e){function t(n){if(r[n])return r[n].exports;var o=r[n]={exports:{},id:n,loaded:!1};return e[n].call(o.exports,o,o.exports,t),o.loaded=!0,o.exports}var r={};return t.m=e,t.c=r,t.p="",t(0)}([function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}Object.defineProperty(t,"__esModule",{value:!0});var o=r(1),i=n(o),a=r(2),s=n(a),u=r(5),c={processor:u.getInterestResults,objectConstructor:u.constructInterestObj},d={processor:u.getTrendingResults,objectConstructor:u.constructTrendingObj},f=i["default"].bind(void 0,s["default"]);t["default"]={autoComplete:f("Auto complete",c),dailyTrends:f("Daily trends",d),interestByRegion:f("Interest by region",c),interestOverTime:f("Interest over time",c),realTimeTrends:f("Real time trends",d),relatedQueries:f("Related queries",c),relatedTopics:f("Related topics",c)},e.exports=t["default"]},function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t["default"]=function(e,t,r){var n=r.processor,o=r.objectConstructor,i=n(e);return function(e,r){var n=o(e,r),a=n.cbFunc,s=n.obj;return s instanceof Error?Promise.reject(a(s)):i(t,s).then(function(e){return a(null,e)})["catch"](function(e){return Promise.reject(a(e))})}},e.exports=t["default"]},function(e,t,r){"use strict";function n(e){return e&&e.__esModule?e:{"default":e}}function o(e,t){var r=void 0;r=s["default"].request(e,function(e){var r="";e.on("data",function(e){r+=e}),e.on("end",function(){t(null,r.toString("utf8"))})}),r.on("error",function(e){t(e)}),r.end()}function i(e){var t=e.method,r=e.host,n=e.path,i=e.qs,a=e.agent,u={host:r,method:t,path:n+"?"+c["default"].stringify(i)};return a&&(u.agent=a),d&&(u.headers={cookie:d}),new Promise(function(e,t){var r=s["default"].request(u,function(r){var n="";r.on("data",function(e){n+=e}),r.on("end",function(){429===r.statusCode&&r.headers["set-cookie"]?(d=r.headers["set-cookie"][0].split(";")[0],u.headers={cookie:d},o(u,function(r,n){return r?t(r):void e(n)})):e(n.toString("utf8"))})});r.on("error",function(e){t(e)}),r.end()})}Object.defineProperty(t,"__esModule",{value:!0}),t["default"]=i;var a=r(3),s=n(a),u=r(4),c=n(u),d=void 0;e.exports=t["default"]},function(e,t){e.exports=require("https")},function(e,t){e.exports=require("querystring")},function(e,t){"use strict";function r(e,t){return Math.abs(t-e)/864e5<7}function n(e,t,r){var n=(e.getUTCMonth()+1).toString(),o=e.getUTCDate().toString(),i=r?"":"-";n=n.length<2?"0"+n:n,o=r&&o.length<2?"0"+o:o;var a=e.getUTCFullYear().toString(),s=e.getUTCHours(),u=e.getUTCMinutes();return t?""+a+i+n+i+o+"T"+s+"\\:"+u+"\\:00":""+a+i+n+i+o}function o(e){if(e.startTime&&e.endTime&&e.startTime>e.endTime){var t=e.startTime;e.startTime=e.endTime,e.endTime=t}e.endTime||(e.endTime=new Date),e.startTime||(e.startTime=new Date("2004-01-01"));var o=r(e.startTime,e.endTime),i=n(e.startTime,o&&e.granularTimeResolution),a=n(e.endTime,o&&e.granularTimeResolution);return e.time=i+" "+a,e}function i(e){var t=Array.isArray(e.geo)&&Array.isArray(e.keyword);return t&&e.geo.length!==e.keyword.length?new Error("Geo length must be equal to keyword length"):e}function a(e){return!e.startTime||e.startTime instanceof Date||(e=new Error("startTime must be a Date object")),!e.endTime||e.endTime instanceof Date||(e=new Error("endTime must be a Date object")),e}function s(e,t){return e?e&&"object"!==("undefined"==typeof e?"undefined":m(e))||Array.isArray(e)?e=new Error("Must supply an object"):e.keyword||(e=new Error("Must have a keyword field")):e=new Error("Must supply an object"),h(t)&&(e=new Error("Callback function must be a function")),e=i(e),e=a(e)}function u(e,t){"function"==typeof e&&(t=e),e=s(e,t),e.hl||(e.hl="en-US"),e.category||(e.category=0),e.timezone||(e.timezone=(new Date).getTimezoneOffset());var r=["images","news","youtube","froogle",""];return r.indexOf(e.property)===-1&&(e.property=""),t||(t=function(e,t){return e?e:t}),e=o(e),{cbFunc:t,obj:e}}function c(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=["COUNTRY","REGION","CITY","DMA"],r=t.some(function(t){return t===e.toUpperCase()});return r?e.toUpperCase():""}function d(e){try{return JSON.parse(e.slice(4)).widgets}catch(t){throw t.requestBody=e,t}}function f(e){var t=e.geo&&Array.isArray(e.geo),r=Array.isArray(e.keyword);if(t&&!r&&(e.keyword=Array(e.geo.length).fill(e.keyword),r=!0),r){var n=function(){var r=e.keyword.reduce(function(t,r){return t.push(y({},e,{keyword:r})),t},[]);return t&&e.geo.forEach(function(e,t){r[t].geo=e}),{v:r}}();if("object"===("undefined"==typeof n?"undefined":m(n)))return n.v}return[e]}function l(e){return function(t,r){var n={"Auto complete":{path:"/trends/api/autocomplete/"+encodeURIComponent(r.keyword)},"Interest over time":{path:"/trends/api/widgetdata/multiline",_id:"TIMESERIES"},"Interest by region":{path:"/trends/api/widgetdata/comparedgeo",resolution:c(r.resolution),_id:"GEO_MAP"},"Related topics":{path:"/trends/api/widgetdata/relatedsearches",_id:"RELATED_TOPICS"},"Related queries":{path:"/trends/api/widgetdata/relatedsearches",_id:"RELATED_QUERIES"}},o={method:"GET",host:"trends.google.com",path:"/trends/api/explore",qs:{hl:r.hl,req:JSON.stringify({comparisonItem:f(r),category:r.category,property:r.property}),tz:r.timezone}};r.agent&&(o.agent=r.agent);var i=n[t],a=i.path,s=i.resolution,u=i._id;return e(o).then(function(n){var o=d(n),i=o.find(function(e){var r=e.id,n=void 0===r?"":r,o=e.request;return n.indexOf(u)>-1||"Auto complete"===t&&o});if(!i){var c={message:"Available widgets does not contain selected api type",requestBody:n};throw c}var f=i.request,l=i.token;s&&(f.resolution=s),f.requestOptions.category=r.category,f.requestOptions.property=r.property,f=JSON.stringify(f);var p={path:a,method:"GET",host:"trends.google.com",qs:{hl:r.hl,req:f,token:l,tz:r.timezone}};return r.agent&&(p.agent=r.agent),e(p)}).then(function(e){try{var t=JSON.stringify(JSON.parse(e.slice(5)));return t}catch(r){return e}})}}function p(e){return function(t,r){var o={"Daily trends":{path:"/trends/api/dailytrends",extraParams:{ed:n(r.trendDate,!1,!0),ns:r.ns}},"Real time trends":{path:"/trends/api/realtimetrends",extraParams:{fi:0,fs:0,ri:300,rs:20,sort:0}}},i={method:"GET",host:"trends.google.com",path:o[t].path,qs:{hl:r.hl,tz:r.timezone,geo:r.geo,cat:r.category}};return r.agent&&(i.agent=r.agent),i.qs=y({},i.qs,o[t].extraParams),e(i).then(function(e){try{return JSON.stringify(JSON.parse(e.slice(5)))}catch(t){return e}})}}function g(e,t){if("function"==typeof e&&(t=e),!e||e&&"object"!==("undefined"==typeof e?"undefined":m(e))||Array.isArray(e))e=new Error("Must supply an object");else{e.trendDate&&e.trendDate instanceof Date||delete e.trendDate;var r=new Date,n={hl:"en-US",category:"all",timezone:r.getTimezoneOffset(),trendDate:r,ns:15};e=y({},n,e)}return h(t)&&(e=new Error("Callback function must be a function")),e.geo||(e=new Error("Must supply an geographical location (geo)")),t||(t=function(e,t){return e?e:t}),{cbFunc:t,obj:e}}Object.defineProperty(t,"__esModule",{value:!0});var y=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};t.isLessThan7Days=r,t.convertDateToString=n,t.formatTime=o,t.constructInterestObj=u,t.formatResolution=c,t.parseResults=d,t.formatComparisonItems=f,t.getInterestResults=l,t.getTrendingResults=p,t.constructTrendingObj=g;var h=function(e){return!!e&&"function"!=typeof e}}])});
//# sourceMappingURL=google-trends-api.min.js.map