{"name": "chromedriver", "version": "137.0.0", "keywords": ["chromedriver", "selenium"], "description": "ChromeDriver for Selenium", "homepage": "https://github.com/giggio/node-chromedriver", "repository": {"type": "git", "url": "git://github.com/giggio/node-chromedriver.git"}, "license": "Apache-2.0", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://www.lambda3.com.br"}, "main": "lib/chromedriver", "bin": {"chromedriver": "bin/chromedriver"}, "scripts": {"install": "node install.js", "update-chromedriver": "node update.js", "typecheck": "tsc", "test": "jest", "test:ci": "jest --ci --reporters=default --reporters=jest-junit", "lint": "eslint --max-warnings=0"}, "dependencies": {"@testim/chrome-version": "^1.1.4", "axios": "^1.7.4", "compare-versions": "^6.1.0", "extract-zip": "^2.0.1", "proxy-agent": "^6.4.0", "proxy-from-env": "^1.1.0", "tcp-port-used": "^1.0.2"}, "devDependencies": {"@eslint/eslintrc": "^3.3.0", "@eslint/js": "^9.22.0", "@types/jest": "^29.5.14", "@types/node": "^22.13.10", "eslint": "^9.22.0", "globals": "^16.0.0", "jest": "^29.7.0", "jest-junit": "^16.0.0", "semver": "^7.7.1", "typescript": "^5.8.2"}, "engines": {"node": ">=18"}}