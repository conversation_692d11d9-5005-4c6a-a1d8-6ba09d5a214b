The tranformation rules in tr_from_brill_paper.txt are from this article:
A simple rule-based part of speech tagger, <PERSON>, Published in: Proceeding ANLC '92 Proceedings of the third conference on Applied natural language processing, Pages 152-155. http://dl.acm.org/citation.cfm?id=974526

The lexicon in lexicon_from_posjs.json is from:
https://github.com/dariusk/pos-js

The transformation rules in tr_from_posj.txt are derived from Javascript code in:
BrillTransformationRules.js from https://github.com/dariusk/pos-js/
