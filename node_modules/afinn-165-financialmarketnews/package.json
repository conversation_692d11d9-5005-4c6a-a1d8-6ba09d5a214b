{"name": "afinn-165-financialmarketnews", "version": "3.0.0", "description": "AFINN 165 (list of English words rated for valence) in JSON with added words and modifications for financial market news", "license": "MIT", "keywords": ["anew", "afinn", "word", "list", "sentiment", "analysis", "opinion", "mining", "text", "microblogs"], "repository": "theryan722/afinn-165-financialmarketnews", "bugs": "https://github.com/words/afinn-165/issues", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}, "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>> (https://wooorm.com)"], "sideEffects": false, "main": "index.js", "types": "index.d.ts", "files": ["index.d.ts", "index.js"], "devDependencies": {"@types/d3-dsv": "^3.0.0", "@types/node": "^18.0.0", "c8": "^7.0.0", "d3-dsv": "^3.0.0", "node-fetch": "^3.0.0", "typescript": "^4.0.0"}, "scripts": {"prepack": "npm run build", "generate": "node build.js", "build": "tsc --build --clean && tsc --build && type-coverage"}}